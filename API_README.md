# 网络请求和环境配置说明

## 项目结构

```
├── api/                    # API接口目录
│   ├── index.uts          # 统一请求实例
│   ├── request.uts        # 基础请求类
│   ├── request-interceptor.uts  # 请求拦截器
│   └── login.uts          # 登录相关API
├── config/                # 环境配置目录
│   ├── env.uts           # 环境管理
│   ├── dev.uts           # 开发环境配置
│   ├── test.uts          # 测试环境配置
│   ├── pre.uts           # 预发环境配置
│   └── prod.uts          # 生产环境配置
├── utils/                 # 工具目录
│   └── request.uts       # 统一导出文件
└── vite.config.ts        # 开发代理配置
```

## 使用方法

### 1. 基础网络请求

```typescript
import { get, post, put, del } from '@/utils/request.uts'

// GET请求
const data = await get('/user/list', { page: 1, size: 10 })

// POST请求
const result = await post('/user/create', { name: '张三', age: 25 })

// PUT请求
const updateResult = await put('/user/update', { id: 1, name: '李四' })

// DELETE请求
const deleteResult = await del('/user/delete', { id: 1 })
```

### 2. 登录相关API

```typescript
import { login, logout, getUserInfo, checkLoginStatus } from '@/utils/request.uts'

// 用户登录
const loginResult = await login({
  username: 'admin',
  password: '123456'
})

// 检查登录状态
const isLoggedIn = await checkLoginStatus()

// 获取用户信息
const userInfo = await getUserInfo()

// 用户登出
await logout()
```

### 3. 环境配置

项目支持四种环境：
- **development**: 开发环境
- **test**: 测试环境
- **pre**: 预发环境
- **production**: 生产环境

```typescript
import { getCurrentEnv, getEnvConfig, isDevelopment } from '@/utils/request.uts'

// 获取当前环境
const currentEnv = getCurrentEnv()

// 获取环境配置
const config = getEnvConfig()

// 判断是否为开发环境
if (isDevelopment()) {
  console.log('当前是开发环境')
}
```

### 4. 自定义拦截器

```typescript
import { requestInterceptor } from '@/utils/request.uts'

// 添加请求拦截器
requestInterceptor.useRequestInterceptor((config) => {
  // 添加自定义请求头
  config.header['Custom-Header'] = 'custom-value'
  return config
})

// 添加响应拦截器
requestInterceptor.useResponseInterceptor((response) => {
  // 处理响应数据
  console.log('响应数据:', response)
  return response
})
```

## 环境配置

### 开发环境 (development)
- 基础URL: `http://localhost:8080`
- 启用调试模式
- 启用请求日志
- 支持热更新

### 测试环境 (test)
- 基础URL: `https://test-api.yourdomain.com`
- 启用调试模式
- 启用请求日志
- 提供测试账号

### 预发环境 (pre)
- 基础URL: `https://pre-api.yourdomain.com`
- 关闭调试模式
- 启用性能监控
- 生产环境数据

### 生产环境 (production)
- 基础URL: `https://api.yourdomain.com`
- 关闭调试模式
- 启用错误上报
- 启用缓存机制

## 本地开发代理

项目在 `manifest.json` 中配置了代理来解决本地开发跨域问题：

```json
{
  "h5": {
    "devServer": {
      "port": 8080,
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        },
        "/upload": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        }
      }
    }
  }
}
```

**注意**: uni-app x项目的代理配置必须写在 `manifest.json` 中，不能使用 `vite.config.ts`。

## 注意事项

1. **UTS语法**: 所有网络请求代码都使用UTS语法编写，确保类型安全
2. **错误处理**: 统一的错误处理机制，自动显示错误提示
3. **Token管理**: 自动添加和刷新Token，处理登录状态
4. **环境切换**: 根据编译环境自动切换配置
5. **跨域处理**: 开发环境通过代理解决跨域问题

## 更新配置

如需修改环境配置，请编辑对应的配置文件：
- 开发环境: `config/dev.uts`
- 测试环境: `config/test.uts`
- 预发环境: `config/pre.uts`
- 生产环境: `config/prod.uts`
