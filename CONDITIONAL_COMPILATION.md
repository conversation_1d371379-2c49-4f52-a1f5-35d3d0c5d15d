# uni-app 条件编译说明

## 什么是条件编译

条件编译是uni-app提供的一种语法，允许在不同平台下编译不同的代码。使用 `#ifdef`、`#ifndef`、`#endif` 等指令。

## 条件编译语法

### 基本语法
```typescript
// #ifdef PLATFORM
// 只在指定平台编译的代码
// #endif

// #ifndef PLATFORM  
// 除了指定平台外都编译的代码
// #endif

// #ifdef PLATFORM1 || PLATFORM2
// 在多个平台编译的代码
// #endif
```

### 平台标识符

| 平台 | 标识符 | 说明 |
|------|--------|------|
| H5 | `H5` | H5平台 |
| 微信小程序 | `MP-WEIXIN` | 微信小程序 |
| 支付宝小程序 | `MP-ALIPAY` | 支付宝小程序 |
| 百度小程序 | `MP-BAIDU` | 百度小程序 |
| 字节跳动小程序 | `MP-TOUTIAO` | 字节跳动小程序 |
| QQ小程序 | `MP-QQ` | QQ小程序 |
| 所有小程序 | `MP` | 所有小程序平台 |
| App | `APP-PLUS` | App平台 |
| Android App | `APP-ANDROID` | Android App |
| iOS App | `APP-IOS` | iOS App |

## IDE支持问题

### 问题现象
在VSCode或HBuilderX中，条件编译的代码可能会显示：
- "此条件编译下的代码不在当前选择的平台中"
- 高亮和语法服务功能已禁用

### 解决方案

#### 方案1：使用运行时判断（推荐）
```typescript
// 不使用条件编译，使用运行时判断
function getPlatformConfig() {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.uniPlatform || systemInfo.platform
  
  if (platform === 'web') {
    return {
      apiUrl: '/api'  // H5使用代理
    }
  } else {
    return {
      apiUrl: 'http://localhost:3000/api'  // 其他平台直接访问
    }
  }
}
```

#### 方案2：配置IDE平台
在HBuilderX中：
1. 点击工具栏的运行按钮
2. 选择要调试的平台
3. IDE会自动识别当前平台，条件编译代码会正常高亮

在VSCode中：
1. 安装uni-app插件
2. 在插件设置中选择目标平台
3. 重启VSCode

#### 方案3：使用配置文件
```typescript
// config/platform.uts
export const platformConfig = {
  h5: {
    apiUrl: '/api'
  },
  mp: {
    apiUrl: 'http://localhost:3000/api'
  },
  app: {
    apiUrl: 'http://localhost:3000/api'
  }
}

// 使用时动态选择
function getApiUrl() {
  const platform = uni.getSystemInfoSync().uniPlatform
  return platformConfig[platform]?.apiUrl || 'http://localhost:3000/api'
}
```

## 最佳实践

### 1. 优先使用运行时判断
```typescript
// ✅ 推荐：运行时判断
function getConfig() {
  const isH5 = uni.getSystemInfoSync().uniPlatform === 'web'
  return {
    apiUrl: isH5 ? '/api' : 'http://localhost:3000/api'
  }
}

// ❌ 不推荐：条件编译（IDE支持不好）
// #ifdef H5
const apiUrl = '/api'
// #endif
// #ifdef MP
const apiUrl = 'http://localhost:3000/api'
// #endif
```

### 2. 集中管理平台差异
```typescript
// utils/platform.uts
export class PlatformUtils {
  static isH5(): boolean {
    return uni.getSystemInfoSync().uniPlatform === 'web'
  }
  
  static isMiniProgram(): boolean {
    const platform = uni.getSystemInfoSync().uniPlatform
    return platform?.startsWith('mp-') || false
  }
  
  static isApp(): boolean {
    return uni.getSystemInfoSync().uniPlatform === 'app'
  }
  
  static getApiUrl(): string {
    if (this.isH5()) {
      return '/api'  // H5使用代理
    } else {
      return 'http://localhost:3000/api'  // 其他平台直接访问
    }
  }
}
```

### 3. 配置文件方式
```typescript
// config/platform-config.uts
interface PlatformConfig {
  apiUrl: string
  wsUrl: string
  cdnUrl: string
}

const configs: Record<string, PlatformConfig> = {
  web: {
    apiUrl: '/api',
    wsUrl: 'ws://localhost:8080/ws',
    cdnUrl: '/static'
  },
  'mp-weixin': {
    apiUrl: 'http://localhost:3000/api',
    wsUrl: 'ws://localhost:3000/ws',
    cdnUrl: 'http://localhost:3000/static'
  },
  app: {
    apiUrl: 'http://localhost:3000/api',
    wsUrl: 'ws://localhost:3000/ws',
    cdnUrl: 'http://localhost:3000/static'
  }
}

export function getPlatformConfig(): PlatformConfig {
  const platform = uni.getSystemInfoSync().uniPlatform || 'app'
  return configs[platform] || configs.app
}
```

## 何时使用条件编译

### 适合使用条件编译的场景
1. **导入不同的模块**
```typescript
// #ifdef H5
import { h5Utils } from './h5-utils'
// #endif
// #ifdef MP-WEIXIN
import { wxUtils } from './wx-utils'  
// #endif
```

2. **平台特有的API调用**
```typescript
// #ifdef MP-WEIXIN
wx.login({
  success: (res) => {
    console.log(res.code)
  }
})
// #endif
```

### 不适合使用条件编译的场景
1. **简单的配置差异** - 使用运行时判断
2. **URL地址差异** - 使用配置文件
3. **业务逻辑差异** - 使用策略模式

## 项目中的实现

我们的项目采用了运行时判断的方式，避免了条件编译的IDE支持问题：

```typescript
// config/env.uts
function getPlatformApiConfig(env: EnvType) {
  const isH5 = uni.getSystemInfoSync().uniPlatform === 'web'
  
  if (env === EnvType.DEVELOPMENT) {
    if (isH5) {
      return { apiUrl: '/api' }  // H5使用代理
    } else {
      return { apiUrl: 'http://localhost:3000/api' }  // 其他平台直接访问
    }
  }
  // ... 其他环境配置
}
```

这种方式的优点：
- ✅ IDE完全支持，无警告
- ✅ 代码高亮正常
- ✅ 智能提示完整
- ✅ 调试方便
- ✅ 类型安全
