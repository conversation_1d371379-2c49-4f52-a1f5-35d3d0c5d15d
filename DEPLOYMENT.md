# 部署说明文档

## 项目概述

本项目已完成以下功能的UTS代码重构：

1. ✅ 网络请求和拦截器改为UTS代码
2. ✅ 建立多环境配置文件（开发、测试、预发、生产）
3. ✅ 建立代理文件解决本地开发跨域问题
4. ✅ 在api目录下建立login.uts
5. ✅ 在login.uvue中调用login.uts中的登录请求

## 项目结构

```
├── api/                          # API接口目录
│   ├── index.uts                # 统一请求实例
│   ├── request.uts              # 基础请求类（UTS重构）
│   ├── request-interceptor.uts  # 请求拦截器（UTS重构）
│   └── login.uts                # 登录相关API（新增）
├── config/                      # 环境配置目录（新增）
│   ├── env.uts                 # 环境管理
│   ├── dev.uts                 # 开发环境配置
│   ├── test.uts                # 测试环境配置
│   ├── pre.uts                 # 预发环境配置
│   └── prod.uts                # 生产环境配置
├── pages/
│   ├── index/index.uvue        # 首页（已更新）
│   ├── login/login.uvue        # 登录页（已更新调用API）
│   └── example/api-example.uvue # API示例页（新增）
├── scripts/                    # 脚本目录（新增）
│   └── setup.uts              # 项目初始化脚本
├── test/                       # 测试目录（新增）
│   └── api-test.uts           # API测试文件
├── utils/                      # 工具目录（新增）
│   └── request.uts            # 统一导出文件
├── manifest.json              # 项目配置（已更新代理配置）
├── package.json               # 项目依赖（新增）
└── API_README.md              # API使用说明（新增）
```

## 环境配置

### 开发环境启动

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 本地代理配置已在 `manifest.json` 中设置，解决跨域问题

### 不同环境构建

```bash
# 开发环境构建
npm run build:dev

# 测试环境构建
npm run build:test

# 预发环境构建
npm run build:pre

# 生产环境构建
npm run build:prod
```

## 主要功能

### 1. 网络请求（UTS重构）

- **基础请求类**: `api/request.uts` - 完全使用UTS语法重写
- **请求拦截器**: `api/request-interceptor.uts` - UTS类型安全的拦截器
- **统一实例**: `api/index.uts` - 预配置的请求实例

### 2. 多环境配置

- **开发环境**: `http://localhost:8080` - 启用调试和日志
- **测试环境**: `https://test-api.yourdomain.com` - 测试服务器
- **预发环境**: `https://pre-api.yourdomain.com` - 预发服务器
- **生产环境**: `https://api.yourdomain.com` - 生产服务器

### 3. 登录功能

- **登录API**: `api/login.uts` - 完整的登录相关接口
- **登录页面**: `pages/login/login.uvue` - 已集成登录API调用
- **状态管理**: 自动处理Token存储和刷新

### 4. 跨域解决方案

- **开发代理**: `manifest.json` 配置本地代理（uni-app x特有）
- **生产环境**: 服务器端配置CORS

## 使用示例

### 基础网络请求

```typescript
import { get, post } from '@/utils/request.uts'

// GET请求
const userData = await get('/user/info')

// POST请求
const result = await post('/user/update', { name: '张三' })
```

### 登录功能

```typescript
import { login, checkLoginStatus } from '@/utils/request.uts'

// 用户登录
const loginResult = await login({
  username: 'admin',
  password: '123456'
})

// 检查登录状态
const isLoggedIn = await checkLoginStatus()
```

## 测试

项目包含以下测试功能：

1. **API测试页面**: `/pages/example/api-example` - 可视化测试界面
2. **测试脚本**: `test/api-test.uts` - 自动化测试脚本

## 注意事项

1. **UTS语法**: 所有网络请求代码都使用UTS语法，确保类型安全
2. **环境切换**: 根据构建命令自动切换环境配置
3. **错误处理**: 统一的错误处理和用户提示
4. **Token管理**: 自动处理Token过期和刷新
5. **跨域处理**: 开发环境通过代理，生产环境需服务器配置

## 后续开发建议

1. 根据实际后端API调整接口地址和参数
2. 完善错误处理和用户体验
3. 添加更多业务相关的API接口
4. 完善单元测试和集成测试
5. 优化性能和缓存策略

## 技术栈

- **框架**: uni-app x
- **语言**: UTS (TypeScript for uni-app)
- **构建工具**: Vite
- **开发工具**: VSCode + uni-app插件
