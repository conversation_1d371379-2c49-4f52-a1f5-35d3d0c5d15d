# manifest.json 配置说明

## 概述

`manifest.json` 是uni-app项目的核心配置文件，包含了应用的基本信息、平台特定配置、权限设置等。

## 基本配置

```json
{
  "name": "test3",                    // 应用名称
  "appid": "__UNI__A8A0406",         // 应用ID
  "description": "",                  // 应用描述
  "versionName": "1.0.0",            // 版本名称
  "versionCode": "100",              // 版本号
  "uni-app-x": {}                    // uni-app x特有配置
}
```

## H5平台配置

### 开发服务器配置
```json
{
  "h5": {
    "devServer": {
      "port": 8080,                   // 开发服务器端口
      "disableHostCheck": true,       // 禁用主机检查
      "proxy": {                      // 代理配置
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        }
      }
    },
    "publicPath": "/",                // 公共路径
    "router": {
      "mode": "hash"                  // 路由模式
    }
  }
}
```

## 小程序平台配置

### 微信小程序
```json
{
  "mp-weixin": {
    "appid": "",                      // 微信小程序AppID
    "setting": {
      "urlCheck": false,              // 不检查安全域名
      "es6": true,                    // 启用ES6转ES5
      "postcss": true,                // 启用postcss
      "minified": true,               // 启用代码压缩
      "coverView": true,              // 启用原生组件同层渲染
      "autoAudits": false,            // 关闭自动检测
      "showShadowRootInWxmlPanel": true,
      "uglifyFileName": false,        // 不混淆文件名
      "checkInvalidKey": true,        // 检查无效key
      "checkSiteMap": true,           // 检查sitemap
      "uploadWithSourceMap": true,    // 上传时包含sourcemap
      "compileHotReLoad": false,      // 关闭热重载
      "lazyloadPlaceholderEnable": false,
      "preloadBackgroundData": false,
      "minifyWXSS": true,            // 压缩样式文件
      "minifyWXML": true             // 压缩模板文件
    },
    "usingComponents": true,          // 启用自定义组件
    "permission": {                   // 权限配置
      "scope.userLocation": {
        "desc": "您的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "requiredPrivateInfos": [         // 必需的隐私信息
      "getLocation"
    ],
    "lazyCodeLoading": "requiredComponents"  // 懒加载
  }
}
```

### 其他小程序平台
```json
{
  "mp-alipay": {                      // 支付宝小程序
    "usingComponents": true,
    "component2": true,
    "enableAppxNg": true,
    "axmlStrictCheck": false,
    "enableParallelLoader": false,
    "enableDistFileMinify": false
  },
  "mp-baidu": {                       // 百度小程序
    "usingComponents": true,
    "appid": "",
    "setting": {
      "urlCheck": false
    }
  },
  "mp-toutiao": {                     // 字节跳动小程序
    "usingComponents": true,
    "appid": "",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "postcss": true,
      "minified": true
    }
  },
  "mp-qq": {                          // QQ小程序
    "appid": "",
    "setting": {
      "urlCheck": false
    },
    "usingComponents": true
  }
}
```

## App平台配置

### 通用App配置
```json
{
  "app": {
    "distribute": {
      "icons": {                      // 图标配置
        "android": {
          "hdpi": "static/icon/icon-72x72.png",
          "xhdpi": "static/icon/icon-96x96.png",
          "xxhdpi": "static/icon/icon-144x144.png",
          "xxxhdpi": "static/icon/icon-192x192.png"
        },
        "ios": {
          // iOS图标配置...
        }
      },
      "splashscreen": {               // 启动页配置
        "android": {
          "hdpi": "static/splash/splash-480x762.png",
          "xhdpi": "static/splash/splash-720x1242.png",
          "xxhdpi": "static/splash/splash-1080x1882.png"
        },
        "ios": {
          // iOS启动页配置...
        }
      }
    }
  }
}
```

### Android平台配置
```json
{
  "app-android": {
    "distribute": {
      "modules": {                    // 模块配置
        "Bluetooth": {},              // 蓝牙
        "Camera": {},                 // 摄像头
        "Contacts": {},               // 通讯录
        "Fingerprint": {},            // 指纹
        "Maps": {},                   // 地图
        "Payment": {},                // 支付
        "Push": {},                   // 推送
        "Share": {},                  // 分享
        "SQLite": {},                 // 数据库
        // 更多模块...
      },
      "permissions": [                // 权限配置
        "<uses-permission android:name=\"android.permission.CAMERA\"/>",
        "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
        // 更多权限...
      ],
      "abiFilters": [                 // CPU架构
        "armeabi-v7a",
        "arm64-v8a"
      ],
      "targetSdkVersion": 33,         // 目标SDK版本
      "minSdkVersion": 21,            // 最小SDK版本
      "schemes": [                    // URL Scheme
        {
          "name": "saas-mall",
          "value": "saas-mall"
        }
      ]
    }
  }
}
```

### iOS平台配置
```json
{
  "app-ios": {
    "distribute": {
      "modules": {                    // 模块配置
        "Camera": {},
        "Contacts": {},
        "Maps": {},
        "Payment": {},
        "Push": {},
        "Share": {},
        // 更多模块...
      },
      "capabilities": {               // 能力配置
        "entitlements": {
          "com.apple.developer.associated-domains": [
            "applinks:yourdomain.com"
          ]
        }
      },
      "idfa": false,                  // 是否使用IDFA
      "privacyDescription": {         // 隐私描述
        "NSCameraUsageDescription": "此应用需要访问摄像头用于拍照上传",
        "NSPhotoLibraryUsageDescription": "此应用需要访问相册用于选择图片上传",
        "NSLocationWhenInUseUsageDescription": "此应用需要访问位置信息用于定位服务",
        "NSMicrophoneUsageDescription": "此应用需要访问麦克风用于语音功能"
      },
      "schemes": [                    // URL Scheme
        {
          "identifier": "saas-mall",
          "schemes": ["saas-mall"]
        }
      ]
    }
  }
}
```

## 配置注意事项

1. **JSON格式**: manifest.json必须是标准的JSON格式，不支持注释
2. **图标路径**: 所有图标路径都是相对于项目根目录的相对路径
3. **权限配置**: Android权限需要完整的权限声明
4. **版本号**: versionCode必须是整数，每次发布都要递增
5. **AppID**: 各平台的AppID需要在对应平台申请获得

## 开发建议

1. 定期备份manifest.json文件
2. 不同环境使用不同的AppID
3. 合理配置权限，避免申请不必要的权限
4. 及时更新版本号和版本名称
5. 测试各平台配置是否正确
