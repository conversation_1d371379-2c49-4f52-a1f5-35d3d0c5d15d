# 多平台配置对比

## 概述

uni-app x项目支持多个平台，每个平台的网络请求配置方式不同。本文档详细对比各平台的配置差异。

## 平台配置对比表

| 平台 | 代理支持 | 开发环境API地址 | 生产环境API地址 | 特殊配置 |
|------|----------|----------------|----------------|----------|
| **H5** | ✅ 支持 | `/api` (相对路径) | `https://api.domain.com/api` | manifest.json中配置代理 |
| **微信小程序** | ❌ 不支持 | `http://localhost:3000/api` | `https://api.domain.com/api` | 需配置域名白名单 |
| **支付宝小程序** | ❌ 不支持 | `http://localhost:3000/api` | `https://api.domain.com/api` | 需配置域名白名单 |
| **App (Android/iOS)** | ❌ 不支持 | `http://localhost:3000/api` | `https://api.domain.com/api` | 需配置网络权限 |

## 详细配置

### 1. H5平台

#### 开发环境
```json
// manifest.json
{
  "h5": {
    "devServer": {
      "port": 8080,
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        }
      }
    }
  }
}
```

```typescript
// config/dev.uts
// #ifdef H5
apiUrl: '/api',  // 使用相对路径，通过代理转发
// #endif
```

#### 生产环境
```typescript
// config/prod.uts
// #ifdef H5
apiUrl: 'https://api.yourdomain.com/api',  // 直接使用完整URL
// #endif
```

### 2. 微信小程序

#### 开发环境配置
```json
// manifest.json
{
  "mp-weixin": {
    "appid": "你的小程序AppID",
    "setting": {
      "urlCheck": false,  // 关键：不检查域名
      "es6": true,
      "postcss": true,
      "minified": true
    },
    "networkTimeout": {
      "request": 60000,
      "downloadFile": 60000,
      "uploadFile": 60000,
      "connectSocket": 60000
    }
  }
}
```

```typescript
// config/dev.uts
// #ifdef MP-WEIXIN
apiUrl: 'http://localhost:3000/api',  // 直接使用完整URL
// #endif
```

#### 微信开发者工具设置
1. 打开微信开发者工具
2. 点击右上角"详情"
3. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

#### 生产环境配置
1. **在微信公众平台配置域名白名单**:
   - request合法域名: `https://api.yourdomain.com`
   - socket合法域名: `wss://api.yourdomain.com`
   - uploadFile合法域名: `https://api.yourdomain.com`
   - downloadFile合法域名: `https://api.yourdomain.com`

2. **代码配置**:
```typescript
// config/prod.uts
// #ifdef MP-WEIXIN
apiUrl: 'https://api.yourdomain.com/api',
// #endif
```

### 3. App平台 (Android/iOS)

#### 开发环境
```typescript
// config/dev.uts
// #ifdef APP-PLUS
apiUrl: 'http://localhost:3000/api',  // 直接使用完整URL
// #endif
```

#### Android特殊配置
```json
// manifest.json
{
  "app-android": {
    "distribute": {
      "permissions": [
        "<uses-permission android:name=\"android.permission.INTERNET\"/>",
        "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>"
      ]
    }
  }
}
```

#### iOS特殊配置
```json
// manifest.json
{
  "app-ios": {
    "distribute": {
      "privacyDescription": {
        "NSAppTransportSecurity": {
          "NSAllowsArbitraryLoads": true  // 允许HTTP请求（仅开发环境）
        }
      }
    }
  }
}
```

## 环境自动识别

项目已配置自动环境识别，根据不同平台和环境自动选择配置：

```typescript
// config/env.uts
export function initEnv(): void {
  // #ifdef H5
  // H5环境根据域名判断
  const hostname = location.hostname
  if (hostname.includes('localhost')) {
    setCurrentEnv(EnvType.DEVELOPMENT)
  } else {
    setCurrentEnv(EnvType.PRODUCTION)
  }
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序根据版本类型判断
  const accountInfo = uni.getAccountInfoSync()
  if (accountInfo.miniProgram.envVersion === 'develop') {
    setCurrentEnv(EnvType.DEVELOPMENT)
  } else if (accountInfo.miniProgram.envVersion === 'trial') {
    setCurrentEnv(EnvType.TEST)
  } else {
    setCurrentEnv(EnvType.PRODUCTION)
  }
  // #endif
  
  // #ifdef APP-PLUS
  // App环境根据打包模式判断
  if (process.env.NODE_ENV === 'production') {
    setCurrentEnv(EnvType.PRODUCTION)
  } else {
    setCurrentEnv(EnvType.DEVELOPMENT)
  }
  // #endif
}
```

## 开发流程对比

### H5开发流程
1. 启动后端服务: `npm run server`
2. 启动前端项目: `npm run dev`
3. 浏览器自动打开，代理自动生效

### 微信小程序开发流程
1. 启动后端服务: `npm run server`
2. 构建小程序: `npm run dev:mp-weixin`
3. 打开微信开发者工具
4. 关闭域名校验
5. 导入项目进行调试

### App开发流程
1. 启动后端服务: `npm run server`
2. 构建App: `npm run dev:app`
3. 使用HBuilderX或真机调试
4. 确保设备能访问开发服务器

## 常见问题

### 1. 微信小程序网络请求失败
**原因**: 域名不在白名单或未关闭域名校验
**解决**: 开发环境关闭域名校验，生产环境配置白名单

### 2. App无法访问本地服务器
**原因**: 设备与开发机不在同一网络或防火墙阻止
**解决**: 使用设备IP地址或配置网络

### 3. H5代理不生效
**原因**: manifest.json配置错误或服务器未启动
**解决**: 检查配置格式，确保后端服务运行

## 最佳实践

1. **统一管理**: 使用条件编译统一管理不同平台配置
2. **环境区分**: 自动识别环境，减少手动配置
3. **错误处理**: 针对不同平台的网络限制做特殊处理
4. **文档维护**: 及时更新各平台配置说明

## 相关文档

- [微信小程序配置详解](./WECHAT_MINIPROGRAM_CONFIG.md)
- [代理配置说明](./PROXY_CONFIG.md)
- [manifest.json配置](./MANIFEST_CONFIG.md)
