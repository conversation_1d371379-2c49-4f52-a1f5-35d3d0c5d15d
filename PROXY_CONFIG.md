# uni-app x 代理配置说明

## 重要说明

在uni-app x项目中，本地开发代理配置应该写在 `manifest.json` 文件中，而不是 `vite.config.ts`。

## 代理配置

### manifest.json 配置

```json
{
  "h5": {
    "devServer": {
      "port": 8080,
      "disableHostCheck": true,
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        },
        "/upload": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        }
      }
    },
    "publicPath": "/",
    "router": {
      "mode": "hash"
    }
  }
}
```

### 配置说明

1. **devServer.port**: 开发服务器端口，默认8080
2. **devServer.disableHostCheck**: 禁用主机检查，允许外部访问
3. **proxy**: 代理配置对象
   - **"/api"**: 代理所有以 `/api` 开头的请求
   - **target**: 目标服务器地址
   - **changeOrigin**: 改变请求头中的origin字段
   - **secure**: 是否验证SSL证书

## 环境配置适配

### 开发环境配置

由于使用了代理，开发环境的API地址应该使用相对路径：

```typescript
// config/dev.uts
export const devConfig: EnvConfig = {
  baseUrl: '/api',        // 使用相对路径
  apiUrl: '/api',         // 使用相对路径
  cdnUrl: '/static',      // 使用相对路径
  // ...其他配置
}
```

### 生产环境配置

生产环境使用完整的URL：

```typescript
// config/prod.uts
export const prodConfig: EnvConfig = {
  baseUrl: 'https://api.yourdomain.com',
  apiUrl: 'https://api.yourdomain.com/api',
  cdnUrl: 'https://cdn.yourdomain.com',
  // ...其他配置
}
```

## 代理工作原理

1. **开发环境**: 
   - 前端请求 `/api/login` 
   - 代理转发到 `http://localhost:3000/api/login`
   - 后端处理请求并返回响应

2. **生产环境**:
   - 前端请求 `https://api.yourdomain.com/api/login`
   - 直接访问生产服务器

## 常见问题

### 1. 代理不生效

**原因**: 可能是manifest.json配置格式错误或路径不匹配

**解决方案**:
- 检查JSON格式是否正确
- 确认代理路径配置
- 重启开发服务器

### 2. CORS错误

**原因**: 后端服务器没有正确配置CORS

**解决方案**:
- 在代理配置中添加 `"changeOrigin": true`
- 后端服务器配置CORS允许跨域

### 3. WebSocket代理

对于WebSocket连接，需要特殊配置：

```json
{
  "h5": {
    "devServer": {
      "proxy": {
        "/ws": {
          "target": "ws://localhost:3000",
          "ws": true,
          "changeOrigin": true
        }
      }
    }
  }
}
```

## 多环境代理配置

### 开发环境代理

```json
{
  "h5": {
    "devServer": {
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true
        }
      }
    }
  }
}
```

### 测试环境

测试环境通常不需要代理，直接使用完整URL：

```typescript
// config/test.uts
export const testConfig: EnvConfig = {
  apiUrl: 'https://test-api.yourdomain.com/api'
}
```

## 调试代理

### 启用代理日志

在开发过程中，可以通过浏览器开发者工具查看网络请求：

1. 打开浏览器开发者工具
2. 切换到 Network 标签
3. 查看请求的实际地址和响应

### 代理测试

可以通过以下方式测试代理是否正常工作：

1. 在浏览器中直接访问 `http://localhost:8080/api/test`
2. 检查是否正确转发到后端服务器
3. 查看响应数据是否正确

## 注意事项

1. **uni-app x特有**: 代理配置必须在manifest.json中，不能使用vite.config.ts
2. **路径匹配**: 代理路径要与前端请求路径完全匹配
3. **端口冲突**: 确保开发服务器端口不与其他服务冲突
4. **重启服务**: 修改manifest.json后需要重启开发服务器
5. **生产环境**: 生产环境不使用代理，需要服务器端配置CORS

## 最佳实践

1. **统一管理**: 将所有API请求路径统一以 `/api` 开头
2. **环境区分**: 开发环境使用相对路径，生产环境使用绝对路径
3. **错误处理**: 在请求拦截器中统一处理代理可能出现的错误
4. **文档维护**: 及时更新代理配置文档，方便团队协作
