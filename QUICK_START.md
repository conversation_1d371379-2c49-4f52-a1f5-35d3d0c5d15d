# 快速启动指南

## 项目概述

这是一个基于uni-app x的商城小程序项目，已完成网络请求UTS重构和多环境配置。

## 🚀 快速开始

### 1. 环境要求

- Node.js >= 16.0.0
- HBuilderX 或 VSCode + uni-app插件
- uni-app x 开发环境

### 2. 安装依赖

```bash
npm install
```

### 3. 启动开发服务器

```bash
npm run dev
```

或在HBuilderX中直接运行到浏览器。

### 4. 后端服务配置

确保您的后端服务运行在 `http://localhost:3000`，或修改 `manifest.json` 中的代理配置：

```json
{
  "h5": {
    "devServer": {
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",  // 修改为您的后端地址
          "changeOrigin": true,
          "secure": false
        }
      }
    }
  }
}
```

## 📁 项目结构

```
├── api/                    # API接口目录
│   ├── index.uts          # 统一请求实例
│   ├── request.uts        # 基础请求类（UTS）
│   ├── request-interceptor.uts  # 请求拦截器（UTS）
│   └── login.uts          # 登录API
├── config/                # 环境配置
│   ├── env.uts           # 环境管理
│   ├── dev.uts           # 开发环境
│   ├── test.uts          # 测试环境
│   ├── pre.uts           # 预发环境
│   └── prod.uts          # 生产环境
├── pages/                 # 页面目录
│   ├── index/            # 首页
│   ├── login/            # 登录页
│   └── example/          # API示例页
└── utils/                # 工具目录
    └── request.uts       # 统一导出
```

## 🔧 核心功能

### 1. 网络请求（UTS重构）

```typescript
import { get, post } from '@/utils/request.uts'

// GET请求
const data = await get('/user/info')

// POST请求
const result = await post('/user/login', {
  username: 'admin',
  password: '123456'
})
```

### 2. 登录功能

```typescript
import { login, checkLoginStatus } from '@/utils/request.uts'

// 用户登录
const loginResult = await login({
  username: 'admin',
  password: '123456'
})

// 检查登录状态
const isLoggedIn = await checkLoginStatus()
```

### 3. 环境配置

项目支持四种环境，会根据构建命令自动切换：

- **开发环境**: 使用代理，API地址为 `/api`
- **测试环境**: `https://test-api.yourdomain.com`
- **预发环境**: `https://pre-api.yourdomain.com`
- **生产环境**: `https://api.yourdomain.com`

## 🌐 代理配置

### 重要说明

uni-app x项目的代理配置必须写在 `manifest.json` 中，不能使用 `vite.config.ts`。

### 当前配置

```json
{
  "h5": {
    "devServer": {
      "port": 8080,
      "proxy": {
        "/api": {
          "target": "http://localhost:3000",
          "changeOrigin": true,
          "secure": false
        }
      }
    }
  }
}
```

## 📱 测试页面

项目包含一个API测试页面，可以快速验证功能：

1. 启动项目
2. 在首页点击"API示例"
3. 测试各种网络请求功能

## 🔨 构建部署

### 开发环境构建
```bash
npm run build:dev
```

### 测试环境构建
```bash
npm run build:test
```

### 预发环境构建
```bash
npm run build:pre
```

### 生产环境构建
```bash
npm run build:prod
```

## ⚠️ 注意事项

1. **代理配置**: 必须在 `manifest.json` 中配置，修改后需重启服务
2. **UTS语法**: 所有网络请求代码使用UTS语法，确保类型安全
3. **环境切换**: 根据构建命令自动切换环境配置
4. **跨域处理**: 开发环境通过代理解决，生产环境需服务器配置CORS

## 🐛 常见问题

### 1. 代理不生效

- 检查 `manifest.json` 格式是否正确
- 确认后端服务是否启动
- 重启开发服务器

### 2. 请求失败

- 检查网络连接
- 确认API地址是否正确
- 查看浏览器控制台错误信息

### 3. 登录失败

- 确认用户名密码是否正确
- 检查后端登录接口是否正常
- 查看网络请求响应

## 📚 相关文档

- [API使用说明](./API_README.md)
- [代理配置详解](./PROXY_CONFIG.md)
- [部署说明](./DEPLOYMENT.md)

## 🤝 开发建议

1. 根据实际后端API调整接口地址和参数
2. 完善错误处理和用户体验
3. 添加更多业务相关的API接口
4. 完善单元测试和集成测试
5. 优化性能和缓存策略
