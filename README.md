# 商城小程序 (uni-app x)

## 项目简介

基于uni-app x开发的商城小程序，支持H5、微信小程序、App多端。

## 项目结构

```
├── api/                 # API接口
│   ├── index.uts       # 统一请求实例
│   ├── request.uts     # 基础请求类
│   ├── request-interceptor.uts # 请求拦截器
│   └── login.uts       # 登录API
├── config/
│   └── env.uts         # 环境配置
├── pages/              # 页面
├── utils/
│   └── request.uts     # 统一导出
└── manifest.json       # 项目配置
```

## 环境配置

支持4个环境，默认开发环境，可手动切换：

| 环境 | API地址 | 说明 |
|------|---------|------|
| **开发(dev)** | H5用代理`/api`，其他用`http://localhost:3000/api` | 默认环境 |
| **测试(test)** | `https://test-api.yourdomain.com/api` | 测试服务器 |
| **预发(pre)** | `https://pre-api.yourdomain.com/api` | 预发服务器 |
| **线上(prod)** | `https://api.yourdomain.com/api` | 生产服务器 |

### 环境切换

```typescript
import { setEnv, EnvType } from '@/utils/request.uts'

// 切换到测试环境
setEnv(EnvType.TEST)

// 切换到生产环境
setEnv(EnvType.PROD)
```

或在API示例页面中点击按钮切换环境。

## 开发

使用HBuilderX导入项目：
- H5: 运行到浏览器
- 微信小程序: 运行到微信开发者工具
- App: 运行到手机或模拟器

## 网络请求

```typescript
import { get, post, login } from '@/utils/request.uts'

// 基础请求
const data = await get('/user/info')
const result = await post('/user/update', { name: '张三' })

// 登录
const loginResult = await login({ username: 'admin', password: '123456' })
```