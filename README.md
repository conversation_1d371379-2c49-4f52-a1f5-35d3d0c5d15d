# 商城小程序 (uni-app x)

## 项目简介

基于uni-app x开发的商城小程序，支持H5、微信小程序、App多端。

## 项目结构

```
├── api/                 # API接口
│   ├── index.uts       # 统一请求实例
│   ├── request.uts     # 基础请求类
│   ├── request-interceptor.uts # 请求拦截器
│   └── login.uts       # 登录API
├── config/
│   └── env.uts         # 环境配置
├── pages/              # 页面
├── utils/
│   └── request.uts     # 统一导出
└── manifest.json       # 项目配置
```

## 环境配置

支持4个环境，自动识别：

| 环境 | H5判断条件 | 小程序判断条件 | API地址 |
|------|------------|----------------|---------|
| **开发(dev)** | localhost域名 | 开发版 | H5用代理`/api`，其他用`http://localhost:3000/api` |
| **测试(test)** | test域名 | 体验版 | `https://test-api.yourdomain.com/api` |
| **预发(pre)** | pre域名 | - | `https://pre-api.yourdomain.com/api` |
| **线上(prod)** | 其他域名 | 正式版 | `https://api.yourdomain.com/api` |

## 开发

使用HBuilderX导入项目：
- H5: 运行到浏览器
- 微信小程序: 运行到微信开发者工具
- App: 运行到手机或模拟器

## 网络请求

```typescript
import { get, post, login } from '@/utils/request.uts'

// 基础请求
const data = await get('/user/info')
const result = await post('/user/update', { name: '张三' })

// 登录
const loginResult = await login({ username: 'admin', password: '123456' })
```