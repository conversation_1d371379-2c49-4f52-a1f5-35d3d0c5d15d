# 商城小程序 (uni-app x)

## 项目简介

基于uni-app x开发的商城小程序，支持H5、微信小程序、App多端。

## 项目结构

```
├── api/                 # API接口
│   ├── index.uts       # 统一请求实例
│   ├── request.uts     # 基础请求类
│   ├── request-interceptor.uts # 请求拦截器
│   └── login.uts       # 登录API
├── config/
│   └── env.uts         # 环境配置
├── pages/              # 页面
├── utils/
│   └── request.uts     # 统一导出
└── manifest.json       # 项目配置
```

## 环境配置

- **H5开发**: 使用代理 `/api` → `http://localhost:3000/api`
- **小程序/App**: 直接访问 `http://localhost:3000/api`

## 开发

使用HBuilderX导入项目：
- H5: 运行到浏览器
- 微信小程序: 运行到微信开发者工具
- App: 运行到手机或模拟器

## 网络请求

```typescript
import { get, post, login } from '@/utils/request.uts'

// 基础请求
const data = await get('/user/info')
const result = await post('/user/update', { name: '张三' })

// 登录
const loginResult = await login({ username: 'admin', password: '123456' })
```