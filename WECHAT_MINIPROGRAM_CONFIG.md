# 微信小程序开发配置说明

## 重要说明

微信小程序的本地开发代理配置与H5不同，需要特殊处理。

## 微信小程序代理特点

### 1. 不支持传统代理
- 微信小程序运行在微信开发者工具中
- 不能使用H5的devServer代理配置
- 需要直接配置完整的API地址

### 2. 域名白名单限制
- 生产环境必须在微信公众平台配置合法域名
- 开发环境可以通过开发者工具设置跳过域名校验

## 开发环境配置

### 1. 微信开发者工具设置

在微信开发者工具中进行以下设置：

1. **打开微信开发者工具**
2. **点击右上角"详情"**
3. **在"本地设置"中勾选：**
   - ✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
   - ✅ 开启调试模式
   - ✅ 开启热重载

### 2. manifest.json配置

```json
{
  "mp-weixin": {
    "appid": "你的微信小程序AppID",
    "setting": {
      "urlCheck": false,              // 关键：不检查安全域名
      "es6": true,
      "postcss": true,
      "minified": true
    },
    "networkTimeout": {               // 网络超时配置
      "request": 60000,
      "downloadFile": 60000,
      "uploadFile": 60000,
      "connectSocket": 60000
    }
  }
}
```

### 3. 环境配置适配

在 `config/dev.uts` 中使用条件编译：

```typescript
export const devConfig: EnvConfig = {
  // H5环境使用相对路径（代理）
  // #ifdef H5
  apiUrl: '/api',
  // #endif
  
  // 小程序环境使用完整URL（无代理）
  // #ifdef MP
  apiUrl: 'http://localhost:3000/api',
  // #endif
  
  // App环境使用完整URL
  // #ifdef APP-PLUS
  apiUrl: 'http://localhost:3000/api',
  // #endif
}
```

## 生产环境配置

### 1. 域名白名单配置

在微信公众平台（mp.weixin.qq.com）配置：

1. **登录微信公众平台**
2. **进入"开发" -> "开发管理" -> "开发设置"**
3. **配置服务器域名：**
   - **request合法域名**: `https://api.yourdomain.com`
   - **socket合法域名**: `wss://api.yourdomain.com`
   - **uploadFile合法域名**: `https://api.yourdomain.com`
   - **downloadFile合法域名**: `https://api.yourdomain.com`

### 2. 生产环境配置

```typescript
// config/prod.uts
export const prodConfig: EnvConfig = {
  apiUrl: 'https://api.yourdomain.com/api',
  wsUrl: 'wss://api.yourdomain.com/ws',
  cdnUrl: 'https://cdn.yourdomain.com'
}
```

## 环境自动识别

项目已配置自动环境识别：

```typescript
// config/env.uts
// #ifdef MP-WEIXIN
const accountInfo = uni.getAccountInfoSync()
if (accountInfo.miniProgram.envVersion === 'develop') {
  setCurrentEnv(EnvType.DEVELOPMENT)      // 开发版
} else if (accountInfo.miniProgram.envVersion === 'trial') {
  setCurrentEnv(EnvType.TEST)             // 体验版
} else {
  setCurrentEnv(EnvType.PRODUCTION)       // 正式版
}
// #endif
```

## 开发流程

### 1. 本地开发
```bash
# 启动后端服务（端口3000）
npm run server

# 启动uni-app项目
npm run dev:mp-weixin

# 在微信开发者工具中打开项目
```

### 2. 调试步骤
1. 确保后端服务运行在 `http://localhost:3000`
2. 在微信开发者工具中关闭域名校验
3. 查看控制台网络请求是否正常
4. 测试API接口调用

### 3. 发布流程
1. 配置生产环境域名白名单
2. 构建生产版本：`npm run build:prod`
3. 在微信开发者工具中上传代码
4. 提交审核

## 常见问题

### 1. 网络请求失败

**现象**: 小程序中API请求失败，提示域名不在白名单中

**解决方案**:
- 开发环境：在微信开发者工具中关闭域名校验
- 生产环境：在微信公众平台配置合法域名

### 2. 跨域问题

**现象**: 请求被CORS策略阻止

**解决方案**:
- 小程序不存在跨域问题
- 如果出现此错误，检查是否在H5环境下运行
- 确保后端服务器配置了正确的CORS头

### 3. 请求超时

**现象**: 网络请求超时

**解决方案**:
- 检查 `manifest.json` 中的 `networkTimeout` 配置
- 增加超时时间或优化后端响应速度

### 4. 开发者工具代理

**现象**: 想在微信开发者工具中使用代理

**解决方案**:
- 微信开发者工具不支持传统代理
- 可以使用工具如 Charles、Fiddler 进行抓包调试
- 或者配置本地hosts文件

## 最佳实践

### 1. 环境区分
- 使用条件编译区分不同平台的配置
- 开发环境使用localhost，生产环境使用HTTPS域名

### 2. 错误处理
- 针对小程序网络限制做特殊错误处理
- 提供友好的错误提示

### 3. 性能优化
- 合理设置网络超时时间
- 使用请求缓存减少网络请求

### 4. 安全考虑
- 生产环境必须使用HTTPS
- 敏感信息不要在前端存储
- 合理配置域名白名单

## 调试工具

### 1. 微信开发者工具
- 网络面板查看请求详情
- 控制台查看日志输出
- 存储面板查看本地数据

### 2. 真机调试
- 使用微信开发者工具的真机调试功能
- 在真实设备上测试网络请求

### 3. 抓包工具
- Charles: 配置代理抓包
- Fiddler: Windows平台抓包工具
- Wireshark: 网络协议分析
