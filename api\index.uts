import Request from './request.uts'
import RequestInterceptor from './request-interceptor.uts'
import { getApiUrl } from '../config/env.uts'

// 创建请求实例
const request = new Request({
  baseUrl: getApiUrl(),
  timeout: 30000,
  header: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 创建拦截器实例
const requestInterceptor = new RequestInterceptor(request)

// 添加自定义请求拦截器
requestInterceptor.useRequestInterceptor((config) => {
  // 添加基础请求头
  if (config.header == null) {
    config.header = {}
  }

  // 添加应用信息
  config.header['App-Version'] = '1.0.0'
  config.header['Client-Type'] = 'uni-app-x'

  // 开发环境打印请求信息
  console.log('请求配置:', config)

  return config
})

// 添加自定义响应拦截器
requestInterceptor.useResponseInterceptor((response) => {
  // 开发环境打印响应信息
  console.log('响应数据:', response)

  return response
})

// 导出请求方法
export const get = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.get(url, data, options)
}

export const post = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.post(url, data, options)
}

export const put = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.put(url, data, options)
}

export const del = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.delete(url, data, options)
}

// 导出请求实例（用于特殊需求）
export { request, requestInterceptor }
