import Request from './request.uts'
import RequestInterceptor from './request-interceptor.uts'
import { getEnvConfig, initEnv } from '../config/env.uts'

// 初始化环境配置
initEnv()

// 获取当前环境配置
const envConfig = getEnvConfig()

// 创建请求实例
const request = new Request({
  baseUrl: envConfig.apiUrl,
  timeout: 30000,
  header: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// 创建拦截器实例
const requestInterceptor = new RequestInterceptor(request)

// 添加自定义请求拦截器
requestInterceptor.useRequestInterceptor((config) => {
  // 添加设备信息
  const systemInfo = uni.getSystemInfoSync()
  if (config.header == null) {
    config.header = {}
  }
  config.header['Device-Type'] = systemInfo.platform
  config.header['Device-Model'] = systemInfo.model
  config.header['App-Version'] = envConfig.version
  
  // 开发环境打印请求信息
  if (envConfig.debug) {
    console.log('请求配置:', config)
  }
  
  return config
})

// 添加自定义响应拦截器
requestInterceptor.useResponseInterceptor((response) => {
  // 开发环境打印响应信息
  if (envConfig.debug) {
    console.log('响应数据:', response)
  }
  
  return response
})

// 导出请求方法
export const get = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.get(url, data, options)
}

export const post = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.post(url, data, options)
}

export const put = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.put(url, data, options)
}

export const del = (url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> => {
  return requestInterceptor.delete(url, data, options)
}

// 导出请求实例（用于特殊需求）
export { request, requestInterceptor }

// 导出环境配置
export { envConfig }
