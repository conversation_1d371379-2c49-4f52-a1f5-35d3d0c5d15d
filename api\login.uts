import { post, get } from './index.uts'

// 登录请求参数接口
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  captchaId?: string
}

// 登录响应数据接口
export interface LoginResponse {
  code: number
  message: string
  data: {
    token: string
    refreshToken: string
    userInfo: UserInfo
    expires: number
  }
}

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  nickname: string
  avatar: string
  phone: string
  email: string
  status: number
  createTime: string
  updateTime: string
}

// 验证码响应接口
export interface CaptchaResponse {
  code: number
  message: string
  data: {
    captchaId: string
    captchaImage: string
  }
}

// 刷新Token响应接口
export interface RefreshTokenResponse {
  code: number
  message: string
  data: {
    token: string
    refreshToken: string
    expires: number
  }
}

/**
 * 用户登录
 * @param params 登录参数
 * @returns Promise<LoginResponse>
 */
export function login(params: LoginParams): Promise<LoginResponse> {
  return post('/auth/login', {
    username: params.username,
    password: params.password,
    captcha: params.captcha ?? '',
    captchaId: params.captchaId ?? ''
  })
}

/**
 * 获取验证码
 * @returns Promise<CaptchaResponse>
 */
export function getCaptcha(): Promise<CaptchaResponse> {
  return get('/auth/captcha')
}

/**
 * 用户登出
 * @returns Promise<any>
 */
export function logout(): Promise<any> {
  return post('/auth/logout')
}

/**
 * 刷新Token
 * @param refreshToken 刷新令牌
 * @returns Promise<RefreshTokenResponse>
 */
export function refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
  return post('/auth/refresh', {
    refreshToken: refreshToken
  })
}

/**
 * 获取用户信息
 * @returns Promise<UserInfo>
 */
export function getUserInfo(): Promise<UserInfo> {
  return get('/user/info')
}

/**
 * 检查登录状态
 * @returns Promise<boolean>
 */
export function checkLoginStatus(): Promise<boolean> {
  return new Promise((resolve) => {
    const token = uni.getStorageSync('token') as string
    if (!token || token.length == 0) {
      resolve(false)
      return
    }
    
    // 验证token是否有效
    getUserInfo().then(() => {
      resolve(true)
    }).catch(() => {
      // token无效，清除本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('refreshToken')
      uni.removeStorageSync('userInfo')
      resolve(false)
    })
  })
}

/**
 * 保存登录信息到本地存储
 * @param loginData 登录响应数据
 */
export function saveLoginInfo(loginData: LoginResponse): void {
  if (loginData.code == 200 && loginData.data) {
    uni.setStorageSync('token', loginData.data.token)
    uni.setStorageSync('refreshToken', loginData.data.refreshToken)
    uni.setStorageSync('userInfo', JSON.stringify(loginData.data.userInfo))
    uni.setStorageSync('tokenExpires', loginData.data.expires)
  }
}

/**
 * 清除登录信息
 */
export function clearLoginInfo(): void {
  uni.removeStorageSync('token')
  uni.removeStorageSync('refreshToken')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('tokenExpires')
}

/**
 * 获取本地存储的用户信息
 * @returns UserInfo | null
 */
export function getLocalUserInfo(): UserInfo | null {
  const userInfoStr = uni.getStorageSync('userInfo') as string
  if (userInfoStr && userInfoStr.length > 0) {
    try {
      return JSON.parse(userInfoStr) as UserInfo
    } catch (e) {
      console.error('解析用户信息失败:', e)
      return null
    }
  }
  return null
}
