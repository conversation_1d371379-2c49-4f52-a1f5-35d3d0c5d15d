import Request from './request.uts'
import { RequestOptions, ErrorInfo, RequestInterceptorFn, ResponseInterceptorFn } from './types.uts'



// 请求拦截器类
class RequestInterceptor {
  private request: Request
  private requestInterceptors: RequestInterceptorFn[]
  private responseInterceptors: ResponseInterceptorFn[]

  constructor(request: Request) {
    this.request = request
    this.requestInterceptors = []
    this.responseInterceptors = []
    this.initInterceptors()
  }

  // 初始化拦截器
  private initInterceptors(): void {
    // 添加默认请求拦截器
    this.requestInterceptors.push((config: RequestOptions) => {
      return this.defaultRequestInterceptor(config)
    })
    // 添加默认响应拦截器
    this.responseInterceptors.push((response: any) => {
      return this.defaultResponseInterceptor(response)
    })
  }

  // 默认请求拦截器
  private defaultRequestInterceptor(config: RequestOptions): RequestOptions {
    // 添加Token认证
    const token = uni.getStorageSync('token') as string
    if (token != null && token.length > 0) {
      if (config.header == null) {
        config.header = {}
      }
      config.header['Authorization'] = `Bearer ${token}`
    }

    // 添加请求时间戳
    if (config.header == null) {
      config.header = {}
    }
    config.header['Request-Time'] = Date.now().toString()

    return config
  }

  // 默认响应拦截器
  private defaultResponseInterceptor(response: any): any {
    // 处理统一错误码
    const responseObj = response as UTSJSONObject
    const code = responseObj.getNumber('code')
    if (code != null && code != 200) {
      this.handleError({
        code: code,
        message: responseObj.getString('message') ?? '请求失败',
        data: response
      })
      return Promise.reject(response)
    }
    return response
  }

  // 处理错误响应
  private handleError(error: ErrorInfo): void {
    const errorCode = error.code ?? -1
    let errorMessage = error.message ?? '请求失败'

    // 根据错误码显示不同的错误信息
    switch (errorCode) {
      case 401:
        errorMessage = '登录状态已过期，请重新登录'
        // 跳转到登录页面
        uni.navigateTo({ url: '/pages/login/login' })
        break
      case 403:
        errorMessage = '权限不足，无法访问'
        break
      case 500:
        errorMessage = '服务器内部错误，请稍后再试'
        break
      default:
        errorMessage = error.message ?? '请求失败，请稍后再试'
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none'
    })
  }

  // 添加自定义请求拦截器
  useRequestInterceptor(interceptor: RequestInterceptorFn): void {
    this.requestInterceptors.push(interceptor)
  }

  // 添加自定义响应拦截器
  useResponseInterceptor(interceptor: ResponseInterceptorFn): void {
    this.responseInterceptors.push(interceptor)
  }

  // 发起请求时应用拦截器
  sendRequest(options: RequestOptions): Promise<any> {
    let config = options

    // 应用请求拦截器
    for (let i = 0; i < this.requestInterceptors.length; i++) {
      const interceptor = this.requestInterceptors[i]
      const result = interceptor(config)
      if (result != null) {
        config = result
      }
    }

    return this.request.request(config).then(
      // 应用响应拦截器
      (response: any) => {
        let result = response
        for (let i = 0; i < this.responseInterceptors.length; i++) {
          const interceptor = this.responseInterceptors[i]
          const interceptorResult = interceptor(result)
          if (interceptorResult != null) {
            result = interceptorResult
          }
        }
        return result
      }
    )
  }

  // 封装请求方法
  get(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.sendRequest({
      url: url,
      method: 'GET',
      data: data,
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  post(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.sendRequest({
      url: url,
      method: 'POST',
      data: data,
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  put(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.sendRequest({
      url: url,
      method: 'PUT',
      data: data,
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  delete(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.sendRequest({
      url: url,
      method: 'DELETE',
      data: data,
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }
}

// 默认导出
export default RequestInterceptor