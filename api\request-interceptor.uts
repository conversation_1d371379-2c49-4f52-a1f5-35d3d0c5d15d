// utils/request-interceptor.js
export default class RequestInterceptor {
  constructor(request) {
    this.request = request;
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    this.initInterceptors();
  }
  
  // 初始化拦截器
  initInterceptors() {
    // 添加默认请求拦截器
    this.requestInterceptors.push(this.defaultRequestInterceptor.bind(this));
    // 添加默认响应拦截器
    this.responseInterceptors.push(this.defaultResponseInterceptor.bind(this));
  }
  
  // 默认请求拦截器
  defaultRequestInterceptor(config) {
    // 添加Token认证
    const token = uni.getStorageSync('token');
    if (token) {
      config.header['Authorization'] = `Bearer ${token}`;
    }
    
    // 添加请求时间戳
    config.header['Request-Time'] = new Date().getTime();
    
    return config;
  }
  
  // 默认响应拦截器
  defaultResponseInterceptor(response) {
    // 处理统一错误码
    if (response.code && response.code !== 200) {
      this.handleError(response);
      return Promise.reject(response);
    }
    return response;
  }
  
  // 处理错误响应
  handleError(error) {
    const errorCode = error.code || -1;
    let errorMessage = error.message || '请求失败';
    
    // 根据错误码显示不同的错误信息
    switch (errorCode) {
      case 401:
        errorMessage = '登录状态已过期，请重新登录';
        // 跳转到登录页面
        uni.navigateTo({ url: '/pages/login/login' });
        break;
      case 403:
        errorMessage = '权限不足，无法访问';
        break;
      case 500:
        errorMessage = '服务器内部错误，请稍后再试';
        break;
      default:
        errorMessage = error.message || '请求失败，请稍后再试';
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
  }
  
  // 添加自定义请求拦截器
  useRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }
  
  // 添加自定义响应拦截器
  useResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }
  
  // 发起请求时应用拦截器
  request(options) {
    let config = options;
    
    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      config = interceptor(config) || config;
    }
    
    return this.request.request(config).then(
      // 应用响应拦截器
      (response) => {
        for (const interceptor of this.responseInterceptors) {
          response = interceptor(response) || response;
        }
        return response;
      }
    );
  }
  
  // 封装请求方法
  get(url, data = {}, options = {}) {
    return this.request(url, { ...options, method: 'GET', data });
  }
  
  post(url, data = {}, options = {}) {
    return this.request(url, { ...options, method: 'POST', data });
  }
  
  // 其他请求方法...
}