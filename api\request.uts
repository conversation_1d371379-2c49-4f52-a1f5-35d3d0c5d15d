// 网络请求配置接口
export interface RequestOptions {
  url: string
  method?: string
  data?: UTSJSONObject
  header?: UTSJSONObject
  timeout?: number
}

// 响应数据接口
export interface ResponseData {
  code: number
  message: string
  data?: any
}

// 错误信息接口
export interface ErrorInfo {
  code: number
  message: string
  data?: any
}

// 网络请求类
export default class Request {
  private baseUrl: string
  private timeout: number
  private header: UTSJSONObject
  private platform: string

  constructor(options: UTSJSONObject) {
    // 基础配置
    this.baseUrl = options['baseUrl'] as string ?? ''
    this.timeout = options['timeout'] as number ?? 30000
    this.header = options['header'] as UTSJSONObject ?? {
      'content-type': 'application/json'
    }

    // 平台差异化处理
    this.platform = uni.getSystemInfoSync().platform
    this.handlePlatformDifference()
  }

  // 处理不同平台的差异化需求
  private handlePlatformDifference(): void {
    if (this.platform == 'android' || this.platform == 'ios') {
      // App平台特殊处理
      this.header['App-Platform'] = 'uni-app'
    } else if (this.platform == 'mp-weixin') {
      // 微信小程序处理
      this.header['Mini-Program'] = 'weixin'
    }
    // 其他平台...
  }

  // 发起请求的核心方法
  request(options: RequestOptions): Promise<any> {
    // 合并基础配置和自定义配置
    const requestOptions: RequestOptions = {
      url: this.baseUrl + options.url,
      method: options.method ?? 'GET',
      data: options.data ?? {},
      header: Object.assign({}, this.header, options.header ?? {}),
      timeout: this.timeout
    }

    return new Promise<any>((resolve, reject) => {
      uni.request({
        url: requestOptions.url,
        method: requestOptions.method as RequestMethod,
        data: requestOptions.data,
        header: requestOptions.header,
        timeout: requestOptions.timeout,
        success: (res: RequestSuccess<any>) => {
          // 处理成功响应
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            // 处理客户端错误
            const error: ErrorInfo = {
              code: res.statusCode,
              message: (res.data as UTSJSONObject)?.getString('message') ?? '请求失败',
              data: res.data
            }
            reject(error)
          }
        },
        fail: (err: RequestFail) => {
          // 处理请求失败
          const error: ErrorInfo = {
            code: err.errCode ?? -1,
            message: err.errMsg ?? '网络错误，请稍后再试',
            data: err
          }
          reject(error)
        }
      })
    })
  }

  // 封装常用请求方法
  get(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.request({
      url: url,
      data: data,
      method: 'GET',
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  post(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.request({
      url: url,
      data: data,
      method: 'POST',
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  put(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.request({
      url: url,
      data: data,
      method: 'PUT',
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }

  delete(url: string, data: UTSJSONObject = {}, options: UTSJSONObject = {}): Promise<any> {
    return this.request({
      url: url,
      data: data,
      method: 'DELETE',
      header: options['header'] as UTSJSONObject,
      timeout: options['timeout'] as number
    })
  }
}