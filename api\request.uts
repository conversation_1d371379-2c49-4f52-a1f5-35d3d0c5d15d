// utils/request.js
export default class Request {
  constructor(options) {
    // 基础配置
    this.baseUrl = options.baseUrl || '';
    this.timeout = options.timeout || 30000;
    this.header = options.header || {
      'content-type': 'application/json'
    };
    
    // 平台差异化处理
    this.platform = uni.getSystemInfoSync().platform;
    this.handlePlatformDifference();
  }
  
  // 处理不同平台的差异化需求
  handlePlatformDifference() {
    if (this.platform === 'android' || this.platform === 'ios') {
      // App平台特殊处理
      this.header['App-Platform'] = 'uni-app';
    } else if (this.platform === 'mp-weixin') {
      // 微信小程序处理
      this.header['Mini-Program'] = 'weixin';
    }
    // 其他平台...
  }
  
  // 发起请求的核心方法
  request(options) {
    // 合并基础配置和自定义配置
    const requestOptions = {
      ...options,
      url: this.baseUrl + options.url,
      header: {
        ...this.header,
        ...options.header
      },
      timeout: this.timeout,
      method: options.method || 'GET'
    };
    
    return new Promise((resolve, reject) => {
      uni.request({
        ...requestOptions,
        success: (res) => {
          // 处理成功响应
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            // 处理客户端错误
            reject({
              code: res.statusCode,
              message: res.data?.message || '请求失败',
              data: res.data
            });
          }
        },
        fail: (err) => {
          // 处理请求失败
          reject({
            code: err.errCode,
            message: err.errMsg || '网络错误，请稍后再试',
            data: err
          });
        }
      });
    });
  }
  
  // 封装常用请求方法
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'GET',
      ...options
    });
  }
  
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'POST',
      ...options
    });
  }
  
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'PUT',
      ...options
    });
  }
  
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'DELETE',
      ...options
    });
  }
}