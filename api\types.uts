// 拦截器函数类型
export type RequestInterceptorFn = (config: RequestOptions) => RequestOptions | null
export type ResponseInterceptorFn = (response: any) => any | null
export interface RequestOptions {
    url: string
    method?: string
    data?: UTSJSONObject
    header?: UTSJSONObject
    timeout?: number
}

// 响应数据接口
export interface ResponseData {
    code: number
    message: string
    data?: any
}

// 错误信息接口
export interface ErrorInfo {
    code: number
    message: string
    data?: any
}