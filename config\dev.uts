import { EnvConfig } from './env.uts'

// 获取开发环境的平台特定配置
function getDevPlatformConfig() {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.uniPlatform || systemInfo.platform

  if (platform === 'web') {
    // H5环境使用相对路径（通过manifest.json代理）
    return {
      baseUrl: '/api',
      apiUrl: '/api',
      wsUrl: 'ws://localhost:8080/ws',
      cdnUrl: '/static'
    }
  } else {
    // 小程序和App环境使用完整URL（不支持代理）
    return {
      baseUrl: 'http://localhost:3000',
      apiUrl: 'http://localhost:3000/api',
      wsUrl: 'ws://localhost:3000/ws',
      cdnUrl: 'http://localhost:3000/static'
    }
  }
}

// 开发环境配置
export const devConfig: EnvConfig = {
  // 动态获取平台特定配置
  ...getDevPlatformConfig(),
  
  // 应用名称
  appName: '商城小程序-开发版',
  
  // 版本号
  version: '1.0.0-dev',
  
  // 是否开启调试
  debug: true,
  
  // 日志级别
  logLevel: 'debug'
}

// 开发环境特有配置
export const devSpecialConfig = {
  // 是否启用mock数据
  enableMock: true,
  
  // 是否显示调试信息
  showDebugInfo: true,
  
  // 是否启用热更新
  enableHotReload: true,
  
  // 请求超时时间(毫秒)
  requestTimeout: 10000,
  
  // 是否启用请求日志
  enableRequestLog: true,
  
  // 是否跳过登录验证
  skipAuth: false
}
