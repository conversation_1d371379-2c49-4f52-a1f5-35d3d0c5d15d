import { EnvConfig } from './env.uts'

// 开发环境配置
export const devConfig: EnvConfig = {
  // 基础URL
  baseUrl: 'http://localhost:8080',
  
  // API接口地址
  apiUrl: 'http://localhost:8080/api',
  
  // WebSocket地址
  wsUrl: 'ws://localhost:8080/ws',
  
  // CDN地址
  cdnUrl: 'http://localhost:8080/static',
  
  // 应用名称
  appName: '商城小程序-开发版',
  
  // 版本号
  version: '1.0.0-dev',
  
  // 是否开启调试
  debug: true,
  
  // 日志级别
  logLevel: 'debug'
}

// 开发环境特有配置
export const devSpecialConfig = {
  // 是否启用mock数据
  enableMock: true,
  
  // 是否显示调试信息
  showDebugInfo: true,
  
  // 是否启用热更新
  enableHotReload: true,
  
  // 请求超时时间(毫秒)
  requestTimeout: 10000,
  
  // 是否启用请求日志
  enableRequestLog: true,
  
  // 是否跳过登录验证
  skipAuth: false
}
