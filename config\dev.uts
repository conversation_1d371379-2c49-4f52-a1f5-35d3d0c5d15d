import { EnvConfig } from './env.uts'

// 开发环境配置
export const devConfig: EnvConfig = {
  // 基础URL
  // H5环境使用相对路径（通过manifest.json代理）
  // 小程序环境使用完整URL（不支持代理）
  // #ifdef H5
  baseUrl: '/api',
  // #endif
  // #ifdef MP
  baseUrl: 'http://localhost:3000',
  // #endif
  // #ifdef APP-PLUS
  baseUrl: 'http://localhost:3000',
  // #endif

  // API接口地址
  // #ifdef H5
  apiUrl: '/api',
  // #endif
  // #ifdef MP
  apiUrl: 'http://localhost:3000/api',
  // #endif
  // #ifdef APP-PLUS
  apiUrl: 'http://localhost:3000/api',
  // #endif

  // WebSocket地址
  wsUrl: 'ws://localhost:3000/ws',

  // CDN地址
  // #ifdef H5
  cdnUrl: '/static',
  // #endif
  // #ifdef MP
  cdnUrl: 'http://localhost:3000/static',
  // #endif
  // #ifdef APP-PLUS
  cdnUrl: 'http://localhost:3000/static',
  // #endif
  
  // 应用名称
  appName: '商城小程序-开发版',
  
  // 版本号
  version: '1.0.0-dev',
  
  // 是否开启调试
  debug: true,
  
  // 日志级别
  logLevel: 'debug'
}

// 开发环境特有配置
export const devSpecialConfig = {
  // 是否启用mock数据
  enableMock: true,
  
  // 是否显示调试信息
  showDebugInfo: true,
  
  // 是否启用热更新
  enableHotReload: true,
  
  // 请求超时时间(毫秒)
  requestTimeout: 10000,
  
  // 是否启用请求日志
  enableRequestLog: true,
  
  // 是否跳过登录验证
  skipAuth: false
}
