// 环境类型枚举
export enum EnvType {
  DEVELOPMENT = 'development',
  TEST = 'test',
  PRE_PRODUCTION = 'pre',
  PRODUCTION = 'production'
}

// 环境配置接口
export interface EnvConfig {
  baseUrl: string
  apiUrl: string
  wsUrl?: string
  cdnUrl?: string
  appName: string
  version: string
  debug: boolean
  logLevel: string
}

// 当前环境变量
let currentEnv: EnvType = EnvType.DEVELOPMENT

// 获取当前环境
export function getCurrentEnv(): EnvType {
  return currentEnv
}

// 设置当前环境
export function setCurrentEnv(env: EnvType): void {
  currentEnv = env
}

// 判断是否为开发环境
export function isDevelopment(): boolean {
  return currentEnv == EnvType.DEVELOPMENT
}

// 判断是否为测试环境
export function isTest(): boolean {
  return currentEnv == EnvType.TEST
}

// 判断是否为预发环境
export function isPreProduction(): boolean {
  return currentEnv == EnvType.PRE_PRODUCTION
}

// 判断是否为生产环境
export function isProduction(): boolean {
  return currentEnv == EnvType.PRODUCTION
}

// 获取平台特定的API配置
function getPlatformApiConfig(env: EnvType): { baseUrl: string, apiUrl: string, wsUrl: string, cdnUrl: string } {
  const isH5 = uni.getSystemInfoSync().uniPlatform === 'web'

  switch (env) {
    case EnvType.DEVELOPMENT:
      if (isH5) {
        return {
          baseUrl: '/api',
          apiUrl: '/api',
          wsUrl: 'ws://localhost:8080/ws',
          cdnUrl: '/static'
        }
      } else {
        return {
          baseUrl: 'http://localhost:3000',
          apiUrl: 'http://localhost:3000/api',
          wsUrl: 'ws://localhost:3000/ws',
          cdnUrl: 'http://localhost:3000/static'
        }
      }
    case EnvType.TEST:
      return {
        baseUrl: 'https://test-api.example.com',
        apiUrl: 'https://test-api.example.com/api',
        wsUrl: 'wss://test-api.example.com/ws',
        cdnUrl: 'https://test-cdn.example.com'
      }
    case EnvType.PRE_PRODUCTION:
      return {
        baseUrl: 'https://pre-api.example.com',
        apiUrl: 'https://pre-api.example.com/api',
        wsUrl: 'wss://pre-api.example.com/ws',
        cdnUrl: 'https://pre-cdn.example.com'
      }
    case EnvType.PRODUCTION:
      return {
        baseUrl: 'https://api.example.com',
        apiUrl: 'https://api.example.com/api',
        wsUrl: 'wss://api.example.com/ws',
        cdnUrl: 'https://cdn.example.com'
      }
    default:
      return {
        baseUrl: 'http://localhost:3000',
        apiUrl: 'http://localhost:3000/api',
        wsUrl: 'ws://localhost:3000/ws',
        cdnUrl: 'http://localhost:3000/static'
      }
  }
}

// 根据环境获取配置
export function getEnvConfig(): EnvConfig {
  const platformConfig = getPlatformApiConfig(currentEnv)

  switch (currentEnv) {
    case EnvType.DEVELOPMENT:
      return {
        ...platformConfig,
        appName: '商城小程序-开发版',
        version: '1.0.0-dev',
        debug: true,
        logLevel: 'debug'
      }
    case EnvType.TEST:
      return {
        baseUrl: 'https://test-api.example.com',
        apiUrl: 'https://test-api.example.com/api',
        wsUrl: 'wss://test-api.example.com/ws',
        cdnUrl: 'https://test-cdn.example.com',
        appName: '商城小程序-测试版',
        version: '1.0.0-test',
        debug: true,
        logLevel: 'info'
      }
    case EnvType.PRE_PRODUCTION:
      return {
        baseUrl: 'https://pre-api.example.com',
        apiUrl: 'https://pre-api.example.com/api',
        wsUrl: 'wss://pre-api.example.com/ws',
        cdnUrl: 'https://pre-cdn.example.com',
        appName: '商城小程序-预发版',
        version: '1.0.0-pre',
        debug: false,
        logLevel: 'warn'
      }
    case EnvType.PRODUCTION:
      return {
        baseUrl: 'https://api.example.com',
        apiUrl: 'https://api.example.com/api',
        wsUrl: 'wss://api.example.com/ws',
        cdnUrl: 'https://cdn.example.com',
        appName: '商城小程序',
        version: '1.0.0',
        debug: false,
        logLevel: 'error'
      }
    default:
      return {
        baseUrl: 'http://localhost:3000',
        apiUrl: 'http://localhost:3000/api',
        appName: '商城小程序',
        version: '1.0.0',
        debug: true,
        logLevel: 'debug'
      }
  }
}

// 初始化环境配置
export function initEnv(): void {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.uniPlatform || systemInfo.platform

  try {
    if (platform === 'web') {
      // H5环境根据域名判断
      const hostname = location.hostname
      if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
        setCurrentEnv(EnvType.DEVELOPMENT)
      } else if (hostname.includes('test')) {
        setCurrentEnv(EnvType.TEST)
      } else if (hostname.includes('pre')) {
        setCurrentEnv(EnvType.PRE_PRODUCTION)
      } else {
        setCurrentEnv(EnvType.PRODUCTION)
      }
    } else if (platform === 'mp-weixin') {
      // 微信小程序环境
      try {
        const accountInfo = uni.getAccountInfoSync()
        if (accountInfo.miniProgram.envVersion === 'develop') {
          setCurrentEnv(EnvType.DEVELOPMENT)
        } else if (accountInfo.miniProgram.envVersion === 'trial') {
          setCurrentEnv(EnvType.TEST)
        } else {
          setCurrentEnv(EnvType.PRODUCTION)
        }
      } catch (e) {
        // 如果获取账户信息失败，默认使用开发环境
        setCurrentEnv(EnvType.DEVELOPMENT)
      }
    } else if (platform === 'app') {
      // App环境根据打包模式判断
      // 注意：在UTS中可能无法直接访问process.env
      setCurrentEnv(EnvType.DEVELOPMENT)
    } else {
      // 其他平台默认使用生产环境
      setCurrentEnv(EnvType.PRODUCTION)
    }
  } catch (error) {
    console.warn('环境初始化失败，使用默认开发环境:', error)
    setCurrentEnv(EnvType.DEVELOPMENT)
  }

  console.log(`当前平台: ${platform}`)
  console.log(`当前环境: ${getCurrentEnv()}`)
  console.log('环境配置:', getEnvConfig())
}
