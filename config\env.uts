// 环境类型
export enum EnvType {
  DEV = 'dev',
  TEST = 'test',
  PRE = 'pre',
  PROD = 'prod'
}

// 环境配置接口
export interface EnvConfig {
  apiUrl: string
  debug: boolean
  env: EnvType
}

// 当前环境
let currentEnv: EnvType = EnvType.DEV

// 环境域名配置
const ENV_DOMAINS = {
  [EnvType.DEV]: 'http://localhost:8080',
  [EnvType.TEST]: 'https://test-api.yourdomain.com',
  [EnvType.PRE]: 'https://pre-api.yourdomain.com',
  [EnvType.PROD]: 'https://api.yourdomain.com'
}

// 获取当前环境 - 被pages/example/api-example.uvue调用显示环境信息
export function getCurrentEnv(): EnvType {
  return currentEnv
}

// 设置环境 - 可以手动调用切换环境
export function setEnv(env: EnvType): void {
  currentEnv = env
  console.log(`环境已切换为: ${currentEnv}`)
  console.log(`API地址: ${getApiUrl()}`)
}

// 获取API地址 - 被api/index.uts调用
export function getApiUrl(): string {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.uniPlatform || systemInfo.osName || 'unknown'
  return `${ENV_DOMAINS[currentEnv]}/api`  //服务器未启用的临时方案，服务器启用后删除，h5还是用代理
  // H5开发环境使用代理，其他情况使用完整地址
  // if (currentEnv === EnvType.DEV && platform === 'web') {
  //   return '/api'
  // }
  // return `${ENV_DOMAINS[currentEnv]}/api`
}

// 获取环境配置 - 被pages/example/api-example.uvue调用显示环境信息
export function getEnvConfig(): EnvConfig {
  return {
    apiUrl: getApiUrl(),
    debug: currentEnv === EnvType.DEV || currentEnv === EnvType.TEST,
    env: currentEnv
  }
}

// 初始化环境 - 被App.uvue调用
export function initEnv(): void {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.uniPlatform || systemInfo.osName || 'unknown'

  // 默认使用开发环境，可以通过setEnv()手动切换
  currentEnv = EnvType.DEV

  console.log(`当前平台: ${platform}`)
  console.log(`当前环境: ${currentEnv}`)
  console.log(`API地址: ${getApiUrl()}`)
  console.log(`提示: 可以通过 setEnv() 方法切换环境`)
}
