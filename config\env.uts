// 环境配置接口
export interface EnvConfig {
  apiUrl: string
  debug: boolean
}

// 获取API地址 - 被api/index.uts调用
export function getApiUrl(): string {
  const platform = uni.getSystemInfoSync().uniPlatform

  // H5使用代理，其他平台使用完整地址
  if (platform === 'web') {
    return '/api'
  } else {
    return 'http://localhost:3000/api'
  }
}

// 获取环境配置 - 被pages/example/api-example.uvue调用显示环境信息
export function getEnvConfig(): EnvConfig {
  return {
    apiUrl: getApiUrl(),
    debug: true
  }
}
