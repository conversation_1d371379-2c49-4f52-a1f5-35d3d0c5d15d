// 环境类型
export enum EnvType {
  DEV = 'dev',
  TEST = 'test',
  PRE = 'pre',
  PROD = 'prod'
}

// 环境配置接口
export interface EnvConfig {
  apiUrl: string
  debug: boolean
  env: EnvType
}

// 当前环境
let currentEnv: EnvType = EnvType.DEV

// 环境域名配置
const ENV_DOMAINS = {
  [EnvType.DEV]: 'http://localhost:3000',
  [EnvType.TEST]: 'https://test-api.yourdomain.com',
  [EnvType.PRE]: 'https://pre-api.yourdomain.com',
  [EnvType.PROD]: 'https://api.yourdomain.com'
}

// 获取当前环境 - 被pages/example/api-example.uvue调用显示环境信息
export function getCurrentEnv(): EnvType {
  return currentEnv
}

// 获取API地址 - 被api/index.uts调用
export function getApiUrl(): string {
  const platform = uni.getSystemInfoSync().uniPlatform

  // H5开发环境使用代理，其他情况使用完整地址
  if (currentEnv === EnvType.DEV && platform === 'web') {
    return '/api'
  }

  return `${ENV_DOMAINS[currentEnv]}/api`
}

// 获取环境配置 - 被pages/example/api-example.uvue调用显示环境信息
export function getEnvConfig(): EnvConfig {
  return {
    apiUrl: getApiUrl(),
    debug: currentEnv === EnvType.DEV || currentEnv === EnvType.TEST,
    env: currentEnv
  }
}

// 初始化环境 - 被App.uvue调用
export function initEnv(): void {
  const platform = uni.getSystemInfoSync().uniPlatform

  if (platform === 'web') {
    // H5根据域名判断环境
    const hostname = location.hostname
    if (hostname.includes('localhost')) {
      currentEnv = EnvType.DEV
    } else if (hostname.includes('test')) {
      currentEnv = EnvType.TEST
    } else if (hostname.includes('pre')) {
      currentEnv = EnvType.PRE
    } else {
      currentEnv = EnvType.PROD
    }
  } else if (platform === 'mp-weixin') {
    // 微信小程序根据版本判断环境
    try {
      const accountInfo = uni.getAccountInfoSync()
      const envVersion = accountInfo.miniProgram.envVersion
      if (envVersion === 'develop') {
        currentEnv = EnvType.DEV
      } else if (envVersion === 'trial') {
        currentEnv = EnvType.TEST
      } else {
        currentEnv = EnvType.PROD
      }
    } catch (e) {
      currentEnv = EnvType.DEV
    }
  } else {
    // App等其他平台默认开发环境
    currentEnv = EnvType.DEV
  }

  console.log(`当前环境: ${currentEnv}, API地址: ${getApiUrl()}`)
}
