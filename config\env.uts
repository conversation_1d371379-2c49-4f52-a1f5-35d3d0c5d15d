// 环境类型枚举
export enum EnvType {
  DEVELOPMENT = 'development',
  TEST = 'test',
  PRE_PRODUCTION = 'pre',
  PRODUCTION = 'production'
}

// 环境配置接口
export interface EnvConfig {
  baseUrl: string
  apiUrl: string
  wsUrl?: string
  cdnUrl?: string
  appName: string
  version: string
  debug: boolean
  logLevel: string
}

// 当前环境变量
let currentEnv: EnvType = EnvType.DEVELOPMENT

// 获取当前环境
export function getCurrentEnv(): EnvType {
  return currentEnv
}

// 设置当前环境
export function setCurrentEnv(env: EnvType): void {
  currentEnv = env
}

// 判断是否为开发环境
export function isDevelopment(): boolean {
  return currentEnv == EnvType.DEVELOPMENT
}

// 判断是否为测试环境
export function isTest(): boolean {
  return currentEnv == EnvType.TEST
}

// 判断是否为预发环境
export function isPreProduction(): boolean {
  return currentEnv == EnvType.PRE_PRODUCTION
}

// 判断是否为生产环境
export function isProduction(): boolean {
  return currentEnv == EnvType.PRODUCTION
}

// 根据环境获取配置
export function getEnvConfig(): EnvConfig {
  switch (currentEnv) {
    case EnvType.DEVELOPMENT:
      return {
        // #ifdef H5
        baseUrl: '/api',
        apiUrl: '/api',
        wsUrl: 'ws://localhost:8080/ws',
        cdnUrl: '/static',
        // #endif
        // #ifdef MP || APP-PLUS
        baseUrl: 'http://localhost:3000',
        apiUrl: 'http://localhost:3000/api',
        wsUrl: 'ws://localhost:3000/ws',
        cdnUrl: 'http://localhost:3000/static',
        // #endif
        appName: '商城小程序-开发版',
        version: '1.0.0-dev',
        debug: true,
        logLevel: 'debug'
      }
    case EnvType.TEST:
      return {
        baseUrl: 'https://test-api.example.com',
        apiUrl: 'https://test-api.example.com/api',
        wsUrl: 'wss://test-api.example.com/ws',
        cdnUrl: 'https://test-cdn.example.com',
        appName: '商城小程序-测试版',
        version: '1.0.0-test',
        debug: true,
        logLevel: 'info'
      }
    case EnvType.PRE_PRODUCTION:
      return {
        baseUrl: 'https://pre-api.example.com',
        apiUrl: 'https://pre-api.example.com/api',
        wsUrl: 'wss://pre-api.example.com/ws',
        cdnUrl: 'https://pre-cdn.example.com',
        appName: '商城小程序-预发版',
        version: '1.0.0-pre',
        debug: false,
        logLevel: 'warn'
      }
    case EnvType.PRODUCTION:
      return {
        baseUrl: 'https://api.example.com',
        apiUrl: 'https://api.example.com/api',
        wsUrl: 'wss://api.example.com/ws',
        cdnUrl: 'https://cdn.example.com',
        appName: '商城小程序',
        version: '1.0.0',
        debug: false,
        logLevel: 'error'
      }
    default:
      return {
        baseUrl: 'http://localhost:3000',
        apiUrl: 'http://localhost:3000/api',
        appName: '商城小程序',
        version: '1.0.0',
        debug: true,
        logLevel: 'debug'
      }
  }
}

// 初始化环境配置
export function initEnv(): void {
  // 根据编译条件设置环境
  // #ifdef APP-PLUS
  // 根据打包模式判断环境
  if (process.env.NODE_ENV == 'production') {
    setCurrentEnv(EnvType.PRODUCTION)
  } else {
    setCurrentEnv(EnvType.DEVELOPMENT)
  }
  // #endif
  
  // #ifdef H5
  // H5环境根据域名判断
  const hostname = location.hostname
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    setCurrentEnv(EnvType.DEVELOPMENT)
  } else if (hostname.includes('test')) {
    setCurrentEnv(EnvType.TEST)
  } else if (hostname.includes('pre')) {
    setCurrentEnv(EnvType.PRE_PRODUCTION)
  } else {
    setCurrentEnv(EnvType.PRODUCTION)
  }
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序环境 - 根据版本类型判断
  // 开发版和体验版使用开发环境配置，正式版使用生产环境配置
  const accountInfo = uni.getAccountInfoSync()
  if (accountInfo.miniProgram.envVersion === 'develop') {
    setCurrentEnv(EnvType.DEVELOPMENT)
  } else if (accountInfo.miniProgram.envVersion === 'trial') {
    setCurrentEnv(EnvType.TEST)
  } else {
    setCurrentEnv(EnvType.PRODUCTION)
  }
  // #endif

  // #ifdef MP && !MP-WEIXIN
  // 其他小程序环境
  setCurrentEnv(EnvType.PRODUCTION)
  // #endif
  
  console.log(`当前环境: ${getCurrentEnv()}`)
  console.log('环境配置:', getEnvConfig())
}
