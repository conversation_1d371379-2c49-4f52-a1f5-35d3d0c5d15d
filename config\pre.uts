import { EnvConfig } from './env.uts'

// 预发环境配置
export const preConfig: EnvConfig = {
  // 基础URL
  baseUrl: 'https://pre-api.yourdomain.com',
  
  // API接口地址
  apiUrl: 'https://pre-api.yourdomain.com/api',
  
  // WebSocket地址
  wsUrl: 'wss://pre-api.yourdomain.com/ws',
  
  // CDN地址
  cdnUrl: 'https://pre-cdn.yourdomain.com',
  
  // 应用名称
  appName: '商城小程序-预发版',
  
  // 版本号
  version: '1.0.0-pre',
  
  // 是否开启调试
  debug: false,
  
  // 日志级别
  logLevel: 'warn'
}

// 预发环境特有配置
export const preSpecialConfig = {
  // 是否启用mock数据
  enableMock: false,
  
  // 是否显示调试信息
  showDebugInfo: false,
  
  // 是否启用热更新
  enableHotReload: false,
  
  // 请求超时时间(毫秒)
  requestTimeout: 20000,
  
  // 是否启用请求日志
  enableRequestLog: false,
  
  // 是否跳过登录验证
  skipAuth: false,
  
  // 性能监控配置
  performanceMonitor: {
    enabled: true,
    sampleRate: 0.1
  }
}
