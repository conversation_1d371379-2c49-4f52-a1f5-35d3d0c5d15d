import { EnvConfig } from './env.uts'

// 生产环境配置
export const prodConfig: EnvConfig = {
  // 基础URL
  baseUrl: 'https://api.yourdomain.com',
  
  // API接口地址
  apiUrl: 'https://api.yourdomain.com/api',
  
  // WebSocket地址
  wsUrl: 'wss://api.yourdomain.com/ws',
  
  // CDN地址
  cdnUrl: 'https://cdn.yourdomain.com',
  
  // 应用名称
  appName: '商城小程序',
  
  // 版本号
  version: '1.0.0',
  
  // 是否开启调试
  debug: false,
  
  // 日志级别
  logLevel: 'error'
}

// 生产环境特有配置
export const prodSpecialConfig = {
  // 是否启用mock数据
  enableMock: false,
  
  // 是否显示调试信息
  showDebugInfo: false,
  
  // 是否启用热更新
  enableHotReload: false,
  
  // 请求超时时间(毫秒)
  requestTimeout: 30000,
  
  // 是否启用请求日志
  enableRequestLog: false,
  
  // 是否跳过登录验证
  skipAuth: false,
  
  // 性能监控配置
  performanceMonitor: {
    enabled: true,
    sampleRate: 1.0
  },
  
  // 错误上报配置
  errorReport: {
    enabled: true,
    apiUrl: 'https://api.yourdomain.com/error-report'
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    maxAge: 3600000, // 1小时
    maxSize: 50 // 最大缓存条数
  }
}
