import { EnvConfig } from './env.uts'

// 测试环境配置
export const testConfig: EnvConfig = {
  // 基础URL
  baseUrl: 'https://test-api.yourdomain.com',
  
  // API接口地址
  apiUrl: 'https://test-api.yourdomain.com/api',
  
  // WebSocket地址
  wsUrl: 'wss://test-api.yourdomain.com/ws',
  
  // CDN地址
  cdnUrl: 'https://test-cdn.yourdomain.com',
  
  // 应用名称
  appName: '商城小程序-测试版',
  
  // 版本号
  version: '1.0.0-test',
  
  // 是否开启调试
  debug: true,
  
  // 日志级别
  logLevel: 'info'
}

// 测试环境特有配置
export const testSpecialConfig = {
  // 是否启用mock数据
  enableMock: false,
  
  // 是否显示调试信息
  showDebugInfo: true,
  
  // 是否启用热更新
  enableHotReload: false,
  
  // 请求超时时间(毫秒)
  requestTimeout: 15000,
  
  // 是否启用请求日志
  enableRequestLog: true,
  
  // 是否跳过登录验证
  skipAuth: false,
  
  // 测试账号信息
  testAccount: {
    username: 'test_user',
    password: 'test123456'
  }
}
