{"name": "test3", "appid": "__UNI__A8A0406", "description": "", "versionName": "1.0.0", "versionCode": "100", "uni-app-x": {}, "h5": {"devServer": {"port": 8080, "disableHostCheck": true, "proxy": {"/api": {"target": "http://localhost:3000", "changeOrigin": true, "secure": false}, "/upload": {"target": "http://localhost:3000", "changeOrigin": true, "secure": false}}}, "publicPath": "/", "router": {"mode": "hash"}}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minifyWXSS": true, "minifyWXML": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation"], "lazyCodeLoading": "requiredComponents", "optimization": {"subPackages": true}, "networkTimeout": {"request": 60000, "downloadFile": 60000, "uploadFile": 60000, "connectSocket": 60000}}, "uniStatistics": {"enable": false}, "vueVersion": "3", "app": {"distribute": {"icons": {"android": {"hdpi": "static/icon/icon-72x72.png", "xhdpi": "static/icon/icon-96x96.png", "xxhdpi": "static/icon/icon-144x144.png", "xxxhdpi": "static/icon/icon-192x192.png"}, "ios": {"appstore": "static/icon/icon-1024x1024.png", "ipad": {"app": "static/icon/icon-76x76.png", "app@2x": "static/icon/icon-152x152.png", "notification": "static/icon/icon-20x20.png", "notification@2x": "static/icon/icon-40x40.png", "proapp@2x": "static/icon/icon-167x167.png", "settings": "static/icon/icon-29x29.png", "settings@2x": "static/icon/icon-58x58.png", "spotlight": "static/icon/icon-40x40.png", "spotlight@2x": "static/icon/icon-80x80.png"}, "iphone": {"app@2x": "static/icon/icon-120x120.png", "app@3x": "static/icon/icon-180x180.png", "notification@2x": "static/icon/icon-40x40.png", "notification@3x": "static/icon/icon-60x60.png", "settings@2x": "static/icon/icon-58x58.png", "settings@3x": "static/icon/icon-87x87.png", "spotlight@2x": "static/icon/icon-80x80.png", "spotlight@3x": "static/icon/icon-120x120.png"}}}, "splashscreen": {"android": {"hdpi": "static/splash/splash-480x762.png", "xhdpi": "static/splash/splash-720x1242.png", "xxhdpi": "static/splash/splash-1080x1882.png"}, "ios": {"ipad": {"portrait": "static/splash/splash-1536x2048.png", "landscape": "static/splash/splash-2048x1536.png"}, "iphone": {"default": "static/splash/splash-320x480.png", "retina35": "static/splash/splash-640x960.png", "retina40": "static/splash/splash-640x1136.png", "retina47": "static/splash/splash-750x1334.png", "retina55": "static/splash/splash-1242x2208.png", "iphonex": "static/splash/splash-1125x2436.png"}}}}}, "app-android": {"distribute": {"modules": {"Bluetooth": {}, "Camera": {}, "Contacts": {}, "Fingerprint": {}, "iBeacon": {}, "LivePusher": {}, "Maps": {}, "Messaging": {}, "OAuth": {}, "Payment": {}, "Push": {}, "Share": {}, "Speech": {}, "SQLite": {}, "Statistic": {}, "VideoPlayer": {}, "Webview-x5": {}}, "icons": {"hdpi": "static/icon/icon-72x72.png", "xhdpi": "static/icon/icon-96x96.png", "xxhdpi": "static/icon/icon-144x144.png", "xxxhdpi": "static/icon/icon-192x192.png"}, "splashScreens": {"hdpi": "static/splash/splash-480x762.png", "xhdpi": "static/splash/splash-720x1242.png", "xxhdpi": "static/splash/splash-1080x1882.png"}, "schemes": [{"name": "saas-mall", "value": "saas-mall"}], "permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"], "targetSdkVersion": 33, "minSdkVersion": 21}}, "app-ios": {"distribute": {"modules": {"Bluetooth": {}, "Camera": {}, "Contacts": {}, "Fingerprint": {}, "iBeacon": {}, "LivePusher": {}, "Maps": {}, "Messaging": {}, "OAuth": {}, "Payment": {}, "Push": {}, "Share": {}, "Speech": {}, "SQLite": {}, "Statistic": {}, "VideoPlayer": {}}, "icons": {"iphone": {"app@2x": "static/icon/icon-120x120.png", "app@3x": "static/icon/icon-180x180.png", "notification@2x": "static/icon/icon-40x40.png", "notification@3x": "static/icon/icon-60x60.png", "settings@2x": "static/icon/icon-58x58.png", "settings@3x": "static/icon/icon-87x87.png", "spotlight@2x": "static/icon/icon-80x80.png", "spotlight@3x": "static/icon/icon-120x120.png"}, "ipad": {"app": "static/icon/icon-76x76.png", "app@2x": "static/icon/icon-152x152.png", "notification": "static/icon/icon-20x20.png", "notification@2x": "static/icon/icon-40x40.png", "proapp@2x": "static/icon/icon-167x167.png", "settings": "static/icon/icon-29x29.png", "settings@2x": "static/icon/icon-58x58.png", "spotlight": "static/icon/icon-40x40.png", "spotlight@2x": "static/icon/icon-80x80.png"}, "appstore": "static/icon/icon-1024x1024.png"}, "splashScreens": {"iphone": {"retina35": "static/splash/splash-640x960.png", "retina40": "static/splash/splash-640x1136.png", "retina47": "static/splash/splash-750x1334.png", "retina55": "static/splash/splash-1242x2208.png", "iphonex": "static/splash/splash-1125x2436.png"}, "ipad": {"portrait": "static/splash/splash-1536x2048.png", "landscape": "static/splash/splash-2048x1536.png"}}, "capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:yourdomain.com"]}}, "idfa": false, "privacyDescription": {"NSCameraUsageDescription": "此应用需要访问摄像头用于拍照上传", "NSPhotoLibraryUsageDescription": "此应用需要访问相册用于选择图片上传", "NSLocationWhenInUseUsageDescription": "此应用需要访问位置信息用于定位服务", "NSMicrophoneUsageDescription": "此应用需要访问麦克风用于语音功能"}, "schemes": [{"identifier": "saas-mall", "schemes": ["saas-mall"]}]}}}