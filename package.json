{"name": "saas-mall-mini", "version": "1.0.0", "description": "SaaS商城小程序", "main": "main.uts", "scripts": {"dev": "uni build --watch", "build:dev": "uni build --mode development", "build:test": "uni build --mode test", "build:pre": "uni build --mode pre", "build:prod": "uni build --mode production", "serve": "uni serve", "lint": "eslint . --ext .vue,.js,.ts,.uts", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "vue": "^3.0.0"}, "devDependencies": {"@dcloudio/types": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vue/eslint-config-typescript": "^11.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^4.9.0", "vue-tsc": "^1.0.0"}, "uni-app": {"scripts": {}}}