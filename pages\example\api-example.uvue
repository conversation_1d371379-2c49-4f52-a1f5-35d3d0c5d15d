<template>
	<view class="container">
		<view class="header">
			<text class="title">API使用示例</text>
		</view>

		<view class="section">
			<text class="section-title">环境信息</text>
			<view class="info-item">
				<text class="label">当前环境:</text>
				<text class="value">{{ currentEnv }}</text>
			</view>
			<view class="info-item">
				<text class="label">API地址:</text>
				<text class="value">{{ envConfig.apiUrl }}</text>
			</view>
			<view class="info-item">
				<text class="label">调试模式:</text>
				<text class="value">{{ envConfig.debug ? '开启' : '关闭' }}</text>
			</view>
		</view>

		<view class="section">
			<text class="section-title">环境切换</text>
			<view class="button-group">
				<button class="btn" @click="switchEnv('dev')">切换到开发环境</button>
				<button class="btn" @click="switchEnv('test')">切换到测试环境</button>
				<button class="btn" @click="switchEnv('prod')">切换到生产环境</button>
			</view>
		</view>

		<view class="section">
			<text class="section-title">网络请求测试</text>
			<view class="button-group">
				<button class="btn" @click="testGetRequest">测试GET请求</button>
				<button class="btn" @click="testPostRequest">测试POST请求</button>
				<button class="btn" @click="testLoginRequest">测试登录请求</button>
			</view>
		</view>

		<view class="section" v-if="requestResult">
			<text class="section-title">请求结果</text>
			<view class="result-box">
				<text class="result-text">{{ requestResult }}</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
	// import { get, post } from '../../api/index.uts'
	// import { login } from '../../pages/login/api/loginApi.uts'
	// import { getEnvConfig, getCurrentEnv, setEnv, EnvType } from '../../config/env.uts'

	// const envConfig = ref(getEnvConfig())
	// const currentEnv = ref(getCurrentEnv())
	// const requestResult = ref('')

	// // 切换环境
	// const switchEnv = (env : string) => {
	// 	let envType : EnvType
	// 	switch (env) {
	// 		case 'dev':
	// 			envType = EnvType.DEV
	// 			break
	// 		case 'test':
	// 			envType = EnvType.TEST
	// 			break
	// 		case 'prod':
	// 			envType = EnvType.PROD
	// 			break
	// 		default:
	// 			envType = EnvType.DEV
	// 	}

	// 	setEnv(envType)
	// 	// 更新显示
	// 	envConfig.value = getEnvConfig()
	// 	currentEnv.value = getCurrentEnv()

	// 	uni.showToast({
	// 		title: `已切换到${env}环境`,
	// 		icon: 'success'
	// 	})
	// }

	// // 测试GET请求
	// const testGetRequest = async () => {
	// 	try {
	// 		uni.showLoading({ title: '请求中...' })
	// 		const result = await get('/test/get', { test: 'data' })
	// 		requestResult.value = JSON.stringify(result, null, 2)
	// 		uni.showToast({ title: '请求成功', icon: 'success' })
	// 	} catch (error) {
	// 		const errorObj = error as unknown as UTSJSONObject
	// 		requestResult.value = `请求失败: ${errorObj.getString('message') ?? '未知错误'}`
	// 		uni.showToast({ title: '请求失败', icon: 'error' })
	// 	} finally {
	// 		uni.hideLoading()
	// 	}
	// }

	// // 测试POST请求
	// const testPostRequest = async () => {
	// 	try {
	// 		uni.showLoading({ title: '请求中...' })
	// 		const result = await post('/test/post', {
	// 			name: '测试数据',
	// 			timestamp: Date.now()
	// 		})
	// 		requestResult.value = JSON.stringify(result, null, 2)
	// 		uni.showToast({ title: '请求成功', icon: 'success' })
	// 	} catch (error) {
	// 		const errorObj = error as unknown as UTSJSONObject
	// 		requestResult.value = `请求失败: ${errorObj.getString('message') ?? '未知错误'}`
	// 		uni.showToast({ title: '请求失败', icon: 'error' })
	// 	} finally {
	// 		uni.hideLoading()
	// 	}
	// }

	// // 测试登录请求
	// const testLoginRequest = async () => {
	// 	try {
	// 		uni.showLoading({ title: '登录中...' })
	// 		const result = await login({
	// 			username: 'test_user',
	// 			password: 'test123456'
	// 		})
	// 		requestResult.value = JSON.stringify(result, null, 2)
	// 		uni.showToast({ title: '登录成功', icon: 'success' })
	// 	} catch (error) {
	// 		const errorObj = error as unknown as UTSJSONObject
	// 		requestResult.value = `登录失败: ${errorObj.getString('message') ?? '未知错误'}`
	// 		uni.showToast({ title: '登录失败', icon: 'error' })
	// 	} finally {
	// 		uni.hideLoading()
	// 	}
	// }
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.header {
		text-align: center;
		margin-bottom: 40rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.section {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			display: block;
		}
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 0;
		border-bottom: 1px solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		.label {
			font-size: 28rpx;
			color: #666;
		}

		.value {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}

	.button-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.btn {
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 12rpx;
		padding: 20rpx;
		font-size: 28rpx;

		&:active {
			background-color: #0056cc;
		}
	}

	.result-box {
		background-color: #f5f5f5;
		border-radius: 12rpx;
		padding: 20rpx;

		.result-text {
			font-size: 24rpx;
			color: #333;
			line-height: 1.5;
			word-break: break-all;
		}
	}
</style>