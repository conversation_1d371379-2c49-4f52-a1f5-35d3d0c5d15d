<template>
	<view class="container">
		<text class="title">{{title}}</text>
		<view class="button-group">
			<button class="btn" @click="goToLogin">登录页面</button>
			<button class="btn" @click="goToApiExample">API示例</button>
			<!-- <button class="btn" @click="getApi">接口请求示例</button> -->
		</view>
	</view>
</template>

<script setup>
	const title = ref("商城小程序")
	const goToLogin = () => {
		uni.navigateTo({
			url: '/pages/login/login'
		})
	}
	
	const openPop = () => {
	}

	const goToApiExample = () => {
		uni.navigateTo({
			url: '/pages/example/api-example'
		})
	}
</script>

<style lang="scss">
	.container {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		min-height: 100vh;
		background-color: #f8f8f8;
	}

	.title {
		font-size: 48rpx;
		color: #333;
		text-align: center;
		margin-bottom: 80rpx;
		font-weight: bold;
	}

	.button-group {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
		width: 100%;
		max-width: 600rpx;
	}

	.btn {
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 12rpx;
		padding: 30rpx;
		font-size: 32rpx;
	}
</style>
