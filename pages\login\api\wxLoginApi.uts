// import { post } from '../../../api/index.uts'
import { WxLoginParams,WxLoginResponse } from '../model/loginModel.uts'

// /**
//  * 微信登录
//  * @param code 微信授权码
//  * @returns Promise<WxLoginResponse>
//  */
// export function wxLoginApi(code: WxLoginParams): Promise<WxLoginResponse> {
//   return post('/auth/wx-login', { code })
// }
// /**
//  * 保存登录信息到本地存储
//  * @param loginData 登录响应数据
//  */
// export function saveLoginInfo(loginData : WxLoginResponse) : void {
// 	if (loginData.code == 200 && loginData.data) {
// 		uni.setStorageSync('token', loginData.data.token)
// 		uni.setStorageSync('refreshToken', loginData.data.refreshToken)
// 		uni.setStorageSync('userInfo', JSON.stringify(loginData.data.userInfo))
// 		uni.setStorageSync('tokenExpires', loginData.data.expires)
// 	}
// }
// /**
//  * 调起微信登录
//  * @returns Promise<void>
//  */
// export function wxLogin(): Promise<void> {
//   return new Promise((resolve, reject) => {
//     // 判断平台
//     const systemInfo = uni.getSystemInfoSync()
//     if (systemInfo.uniPlatform === 'mp-weixin') {
//       // 微信小程序内直接调用
//       uni.login({
//         provider: 'weixin',
//         success: async (loginRes) => {
//           try {
//             // 获取微信code
//             const code = loginRes.code
//             // 调用后端接口，用code换取用户信息
//             const response = await wxLoginApi(code as WxLoginParams)
            
//             if (response.code == 200) {
//               saveLoginInfo(response)
//               uni.showToast({ title: '登录成功', icon: 'success' })
//               setTimeout(() => uni.navigateBack(), 1500)
//               resolve()
//             } else {
//               uni.showToast({ title: response.message || '登录失败', icon: 'none' })
//               reject(new Error(response.message || '登录失败'))
//             }
//           } catch (error) {
//             console.error('微信登录失败:', error)
//             uni.showToast({ title: '微信登录失败', icon: 'none' })
//             reject(error)
//           }
//         },
//         fail: (err) => {
//           uni.showToast({ title: '微信登录失败', icon: 'none' })
//           reject(new Error('微信登录失败'))
//         }
//       })
//     } else if (systemInfo.osName == 'android' || systemInfo.osName == 'ios') {
//       // App端调用微信SDK
//       uni.login({
//         provider: 'weixin',
//         success: async (loginRes) => {
//           try {
//             // 与小程序类似的处理逻辑
//             const code = loginRes.code
//             const response = await wxLoginApi(code as WxLoginParams)
            
//             if (response.code == 200) {
//               saveLoginInfo(response)
//               uni.showToast({ title: '登录成功', icon: 'success' })
//               setTimeout(() => uni.navigateBack(), 1500)
//               resolve()
//             } else {
//               uni.showToast({ title: response.message || '登录失败', icon: 'none' })
//               reject(new Error(response.message || '登录失败'))
//             }
//           } catch (error) {
//             console.error('微信登录失败:', error)
//             uni.showToast({ title: '微信登录失败', icon: 'none' })
//             reject(error)
//           }
//         },
//         fail: (err) => {
//           console.error('微信登录失败', err)
//           uni.showToast({ title: '微信登录失败', icon: 'none' })
//           reject(new Error('微信登录失败'))
//         }
//       })
//     } else {
//       // H5或其他平台
//       uni.showToast({ title: '当前平台不支持微信登录', icon: 'none' })
//       reject(new Error('当前平台不支持微信登录'))
//     }
//   })
// }