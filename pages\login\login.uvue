<template>
	<view class="page">
		<view class="logo-info">
			<view class="logo">
				<image src='../../static/logo.png' class="image"></image>
			</view>
		</view>
		<view class="login-info">
			<view class="login-account">
				<view class="input-text">
					<input type="text" placeholder="请输入账号" v-model="username" class="input" />
				</view>
				<view class="input-text">
					<input type="password" placeholder="请输入密码" v-model="password" class="input" />
				</view>
				<view class="protocol">
					<radio color="#fe9a5c" :checked="isProtocol" @click="isProtocol = !isProtocol"></radio>
					<text class="text">我已阅读并同意</text>
					<text class="link">《隐私政策》</text>
					<text class="text">和</text>
					<text class="link">《服务协议》</text>
				</view>
				<view class="login-btn">
					<view class="btn active" @click="onLogin">
						<text class="text">登录</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 其他登录方式 -->
		<view class="login-other">
			<view class="title">
				<text class="text">其他登录方式</text>
			</view>
			<view class="login-way">
				<view class="way">
					<image src="../../static/icon/wx_login.png" class="image"></image>
				</view>
				<view class="way">
					<image src="../../static/icon/qq_login.png" class="image"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
	const username = ref("")
	const password = ref("")
	const isProtocol = ref(true)
	const onLogin = () => {
		uni.setStorageSync('isLogin',true);
		uni.navigateBack();
	}
</script>

<style lang="scss">
@import './login.scss';
</style>
