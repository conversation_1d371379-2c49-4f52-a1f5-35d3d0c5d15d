<template>
	<view class="page">
		<view class="logo-info">
			<view class="logo">
				<image src='../../static/logo.png' class="image"></image>
			</view>
		</view>
		<view class="login-info">
			<view class="login-account">
				<view class="input-text">
					<input type="text" placeholder="请输入账号" v-model="username" class="input" />
				</view>
				<view class="input-text">
					<input type="password" placeholder="请输入密码" v-model="password" class="input" />
				</view>
				<view class="protocol">
					<radio color="#fe9a5c" :checked="isProtocol" @click="isProtocol = !isProtocol"></radio>
					<text class="text">我已阅读并同意</text>
					<text class="link">《隐私政策》</text>
					<text class="text">和</text>
					<text class="link">《服务协议》</text>
				</view>
				<view class="login-btn">
					<view class="btn" :class="{ active: !isLoading }" @click="onLogin">
						<text class="text" v-if="!isLoading">登录</text>
						<text class="text" v-else>登录中...</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 其他登录方式 -->
		<view class="login-other">
			<view class="title">
				<text class="text">其他登录方式</text>
			</view>
			<view class="login-way">
				<view class="way" @click="wxLogin">
					<image src="../../static/icon/wx_login.png" class="image"></image>
				</view>
				<view class="way">
					<image src="../../static/icon/qq_login.png" class="image"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
	// import { LoginParams, LoginResponse, WxLoginParams, WxLoginResponse } from './model/loginModel.uts'
	// import { login, saveLoginInfo } from './api/loginApi.uts'
	// import { wxLoginApi } from './api/wxLoginApi.uts'

	const username = ref("")
	const password = ref("")
	const isProtocol = ref(true)
	const isLoading = ref(false)

	// // 登录处理函数
	// const onLogin = async () => {
	// 	// 表单验证
	// 	if (!validateForm()) {
	// 		return
	// 	}

	// 	// 协议验证
	// 	if (!isProtocol.value) {
	// 		uni.showToast({
	// 			title: '请先同意用户协议',
	// 			icon: 'none'
	// 		})
	// 		return
	// 	}

	// 	// 开始登录
	// 	isLoading.value = true

	// 	try {
	// 		const loginParams : LoginParams = {
	// 			username: username.value,
	// 			password: password.value
	// 		}
	// 		const response = await login(loginParams)
	// 		if (response.code == 200) {
	// 			// 保存登录信息
	// 			saveLoginInfo(response)

	// 			uni.showToast({
	// 				title: '登录成功',
	// 				icon: 'success'
	// 			})

	// 			// 延迟跳转，让用户看到成功提示
	// 			setTimeout(() => {
	// 				uni.navigateBack()
	// 			}, 1500)
	// 		} else {
	// 			uni.showToast({
	// 				title: response.message || '登录失败',
	// 				icon: 'none'
	// 			})
	// 		}
	// 	} catch (error) {
	// 		console.error('登录失败:', error)
	// 		const errorObj = error as unknown as UTSJSONObject
	// 		const message = errorObj.getString('message') ?? '网络错误，请稍后重试'
	// 		uni.showToast({
	// 			title: message,
	// 			icon: 'none'
	// 		})
	// 	} finally {
	// 		isLoading.value = false
	// 	}
	// }

	// // 表单验证
	// const validateForm = () : boolean => {
	// 	if (!username.value || username.value.trim().length == 0) {
	// 		uni.showToast({
	// 			title: '请输入用户名',
	// 			icon: 'none'
	// 		})
	// 		return false
	// 	}

	// 	if (!password.value || password.value.trim().length == 0) {
	// 		uni.showToast({
	// 			title: '请输入密码',
	// 			icon: 'none'
	// 		})
	// 		return false
	// 	}

	// 	if (password.value.length < 6) {
	// 		uni.showToast({
	// 			title: '密码长度不能少于6位',
	// 			icon: 'none'
	// 		})
	// 		return false
	// 	}

	// 	return true
	// }
	// // 微信登录处理函数
	// const wxLogin = () => {
	// 	// 判断平台
	// 	const systemInfo = uni.getSystemInfoSync()
	// 	if (systemInfo.uniPlatform === 'mp-weixin') {
	// 		// 微信小程序内直接调用
	// 		uni.login({
	// 			provider: 'weixin',
	// 			success: async (loginRes) => {
	// 				try {
	// 					// 获取微信code
	// 					const code = loginRes.code
	// 					// 调用后端接口，用code换取用户信息
	// 					const response = await wxLoginApi({ code } as WxLoginParams)

	// 					if (response.code == 200) {
	// 						saveLoginInfo(response)
	// 						uni.showToast({ title: '登录成功', icon: 'success' })
	// 						setTimeout(() => uni.navigateBack(), 1500)
	// 					} else {
	// 						uni.showToast({ title: response.message || '登录失败', icon: 'none' })
	// 					}
	// 				} catch (error) {
	// 					console.error('微信登录失败:', error)
	// 					uni.showToast({ title: '微信登录失败', icon: 'none' })
	// 				}
	// 			},
	// 			fail: () => {
	// 				uni.showToast({ title: '微信登录失败', icon: 'none' })
	// 			}
	// 		})
	// 	} else if (systemInfo.osName === 'android' || systemInfo.osName === 'ios') {
	// 		// App端调用微信SDK
	// 		uni.login({
	// 			provider: 'weixin',
	// 			success: (loginRes) => {
	// 				// 与小程序类似的处理逻辑
	// 				console.log('微信登录成功', loginRes)
	// 			},
	// 			fail: (err) => {
	// 				console.error('微信登录失败', err)
	// 				uni.showToast({ title: '微信登录失败', icon: 'none' })
	// 			}
	// 		})
	// 	} else {
	// 		// H5或其他平台
	// 		uni.showToast({ title: '当前平台不支持微信登录', icon: 'none' })
	// 	}
	// }
</script>

<style lang="scss">
	@import './login.scss';
</style>