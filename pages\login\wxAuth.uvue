<template>
  <view class="auth-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-icon">
        <view class="logo-bg">
          <text class="logo-text"></text>
        </view>
      </view>
    </view>

    <!-- 权限说明 -->
    <view class="permission-section">
      <text class="permission-title">登录后开发者将获得以下权限</text>
      <text class="permission-desc">获得你的公开信息（昵称、头像等）</text>
    </view>

    <!-- 按钮区域 -->
    <view class="button-section">
      <!-- 允许授权按钮 -->
      <button class="auth-btn primary" @click="onAllowAuth">
        允许授权
      </button>

      <!-- 暂不授权按钮 -->
      <button class="auth-btn secondary" @click="onDenyAuth">
        暂不授权
      </button>
    </view>
  </view>
</template>

<script setup lang="uts">
  // 允许授权
  const onAllowAuth = () => {
    // 微信授权逻辑
    uni.showToast({
      title: '授权成功',
      icon: 'success'
    })

    // 可以在这里处理授权成功后的逻辑
    // 比如获取用户信息、跳转到主页等
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }, 1500)
  }

  // 暂不授权
  const onDenyAuth = () => {
    uni.showToast({
      title: '已取消授权',
      icon: 'none'
    })

    // 返回上一页或跳转到其他页面
    uni.navigateBack()
  }

  // 打开用户协议
  const openUserAgreement = () => {
    uni.showToast({
      title: '用户服务协议',
      icon: 'none'
    })
  }

  // 打开隐私政策
  const openPrivacyPolicy = () => {
    uni.showToast({
      title: '隐私政策',
      icon: 'none'
    })
  }

  // 打开法律声明
  const openLegalStatement = () => {
    uni.showToast({
      title: '法律声明',
      icon: 'none'
    })
  }
</script>

<style lang="scss">
.auth-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  padding: 0 48rpx;
}

.logo-section {
  margin-top: 200rpx;
  display: flex;
  align-items: center;
  margin-bottom: 120rpx;

  .logo-icon {
    margin-right: 24rpx;

    .logo-bg {
      width: 96rpx;
      height: 96rpx;
      background-color: #00C853;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-text {
        color: white;
        font-size: 36rpx;
        font-weight: bold;
      }
    }
  }
}

.permission-section {
  margin-bottom: 300rpx;

  .permission-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    display: block;
    margin-bottom: 16rpx;
  }

  .permission-desc {
    font-size: 28rpx;
    color: #666;
    display: block;
  }
}

.button-section {
  margin-bottom: 120rpx;

  .auth-btn {
    width: 100%;
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 32rpx;
    border: none;

    &.primary {
      background-color: #00C853;
      color: white;

      &:active {
        background-color: #00A047;
      }
    }

    &.secondary {
      background: none;
      color: #999;
      font-size: 28rpx;

      &:active {
        color: #666;
      }
    }
  }
}
</style>