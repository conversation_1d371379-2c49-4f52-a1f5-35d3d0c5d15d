<template>
  <view class="login-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-icon">
        <view class="logo-bg">
          <text class="logo-text">荷</text>
        </view>
      </view>
      <view class="brand-info">
        <text class="brand-name">荷叶健康</text>
        <text class="brand-english">LOTUS HEALTH</text>
      </view>
    </view>

    <!-- 权限说明 -->
    <view class="permission-section">
      <text class="permission-title">登录后开发者将获得以下权限</text>
      <text class="permission-desc">获得你的公开信息（昵称、头像等）</text>
    </view>

    <!-- 按钮区域 -->
    <view class="button-section">
      <!-- 一键登录按钮 -->
      <button class="login-btn primary" @click="onWxLogin">
        一键登录
      </button>

      <!-- 手机验证码登录按钮 -->
      <button class="login-btn secondary" @click="onPhoneLogin">
        手机验证码登录/注册
      </button>

      <!-- 暂不登录 -->
      <button class="skip-btn" @click="onSkipLogin">
        暂不登录/注册
      </button>
    </view>
  </view>
</template>

<script setup lang="uts">
  const isAgreed = ref(false)

  // 协议勾选状态改变
  const onAgreementChange = (e: any) => {
    isAgreed.value = e.detail.value.length > 0
  }

  // 微信一键登录
  const onWxLogin = () => {
    if (!isAgreed.value) {
      uni.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 微信登录逻辑
    uni.showToast({
      title: '微信登录功能开发中',
      icon: 'none'
    })
  }

  // 手机验证码登录
  const onPhoneLogin = () => {
    if (!isAgreed.value) {
      uni.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 跳转到手机登录页面
    uni.navigateTo({
      url: '/pages/login/login'
    })
  }

  // 暂不登录
  const onSkipLogin = () => {
    uni.navigateBack()
  }

  // 打开用户协议
  const openUserAgreement = () => {
    uni.showToast({
      title: '用户服务协议',
      icon: 'none'
    })
  }

  // 打开隐私政策
  const openPrivacyPolicy = () => {
    uni.showToast({
      title: '隐私政策',
      icon: 'none'
    })
  }

  // 打开法律声明
  const openLegalStatement = () => {
    uni.showToast({
      title: '法律声明',
      icon: 'none'
    })
  }
</script>

<style lang="scss">
.login-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  padding: 0 48rpx;
}

.logo-section {
  margin-top: 200rpx;
  display: flex;
  align-items: center;
  margin-bottom: 120rpx;

  .logo-icon {
    margin-right: 24rpx;

    .logo-bg {
      width: 96rpx;
      height: 96rpx;
      background-color: #00C853;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-text {
        color: white;
        font-size: 36rpx;
        font-weight: bold;
      }
    }
  }

  .brand-info {
    display: flex;
    flex-direction: column;

    .brand-name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 8rpx;
    }

    .brand-english {
      font-size: 24rpx;
      color: #666;
      letter-spacing: 2rpx;
    }
  }
}

.permission-section {
  margin-bottom: 200rpx;

  .permission-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    display: block;
    margin-bottom: 16rpx;
  }

  .permission-desc {
    font-size: 28rpx;
    color: #666;
    display: block;
  }
}

.button-section {
  margin-bottom: 80rpx;

  .login-btn {
    width: 100%;
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 24rpx;
    border: none;

    &.primary {
      background-color: #00C853;
      color: white;

      &:active {
        background-color: #00A047;
      }
    }

    &.secondary {
      background-color: white;
      color: #333;
      border: 2rpx solid #e0e0e0;

      &:active {
        background-color: #f5f5f5;
      }
    }
  }

  .skip-btn {
    background: none;
    border: none;
    color: #999;
    font-size: 28rpx;
    padding: 20rpx 0;

    &:active {
      color: #666;
    }
  }
}

</style>