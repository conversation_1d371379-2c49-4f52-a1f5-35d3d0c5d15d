// 项目初始化脚本
import { initEnv, getCurrentEnv, getEnvConfig } from '../config/env.uts'

// 初始化项目
export function setupProject(): void {
  console.log('=== 项目初始化开始 ===')
  
  // 初始化环境配置
  initEnv()
  
  // 显示当前环境信息
  const currentEnv = getCurrentEnv()
  const envConfig = getEnvConfig()
  
  console.log(`当前环境: ${currentEnv}`)
  console.log('环境配置:', envConfig)
  
  // 检查必要的存储权限
  checkStoragePermission()
  
  // 检查网络权限
  checkNetworkPermission()
  
  console.log('=== 项目初始化完成 ===')
}

// 检查存储权限
function checkStoragePermission(): void {
  try {
    uni.setStorageSync('test_key', 'test_value')
    const testValue = uni.getStorageSync('test_key')
    if (testValue == 'test_value') {
      console.log('✓ 存储权限正常')
      uni.removeStorageSync('test_key')
    } else {
      console.warn('⚠ 存储权限异常')
    }
  } catch (error) {
    console.error('✗ 存储权限检查失败:', error)
  }
}

// 检查网络权限
function checkNetworkPermission(): void {
  uni.getNetworkType({
    success: (res) => {
      console.log(`✓ 网络权限正常，当前网络类型: ${res.networkType}`)
    },
    fail: (error) => {
      console.error('✗ 网络权限检查失败:', error)
    }
  })
}

// 清理项目缓存
export function clearProjectCache(): void {
  console.log('=== 清理项目缓存 ===')
  
  try {
    // 清理登录信息
    uni.removeStorageSync('token')
    uni.removeStorageSync('refreshToken')
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('tokenExpires')
    
    // 清理其他缓存
    uni.removeStorageSync('appCache')
    uni.removeStorageSync('userSettings')
    
    console.log('✓ 缓存清理完成')
  } catch (error) {
    console.error('✗ 缓存清理失败:', error)
  }
}

// 检查应用更新
export function checkAppUpdate(): void {
  console.log('=== 检查应用更新 ===')
  
  // #ifdef APP-PLUS
  const updateManager = uni.getUpdateManager()
  
  updateManager.onCheckForUpdate((res) => {
    console.log('检查更新结果:', res.hasUpdate)
  })
  
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: (res) => {
        if (res.confirm) {
          updateManager.applyUpdate()
        }
      }
    })
  })
  
  updateManager.onUpdateFailed(() => {
    uni.showModal({
      title: '更新失败',
      content: '新版本下载失败，请检查网络后重试',
      showCancel: false
    })
  })
  // #endif
  
  // #ifdef MP
  console.log('小程序环境，更新由平台管理')
  // #endif
  
  // #ifdef H5
  console.log('H5环境，请手动刷新页面获取最新版本')
  // #endif
}
