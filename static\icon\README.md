# 应用图标说明

## 图标尺寸要求

### Android 图标
- **hdpi**: 72x72px (icon-72x72.png)
- **xhdpi**: 96x96px (icon-96x96.png)
- **xxhdpi**: 144x144px (icon-144x144.png)
- **xxxhdpi**: 192x192px (icon-192x192.png)

### iOS 图标

#### iPhone
- **app@2x**: 120x120px (icon-120x120.png)
- **app@3x**: 180x180px (icon-180x180.png)
- **notification@2x**: 40x40px (icon-40x40.png)
- **notification@3x**: 60x60px (icon-60x60.png)
- **settings@2x**: 58x58px (icon-58x58.png)
- **settings@3x**: 87x87px (icon-87x87.png)
- **spotlight@2x**: 80x80px (icon-80x80.png)
- **spotlight@3x**: 120x120px (icon-120x120.png)

#### iPad
- **app**: 76x76px (icon-76x76.png)
- **app@2x**: 152x152px (icon-152x152.png)
- **notification**: 20x20px (icon-20x20.png)
- **notification@2x**: 40x40px (icon-40x40.png)
- **proapp@2x**: 167x167px (icon-167x167.png)
- **settings**: 29x29px (icon-29x29.png)
- **settings@2x**: 58x58px (icon-58x58.png)
- **spotlight**: 40x40px (icon-40x40.png)
- **spotlight@2x**: 80x80px (icon-80x80.png)

#### App Store
- **appstore**: 1024x1024px (icon-1024x1024.png)

## 图标设计要求

1. **格式**: PNG格式，透明背景
2. **圆角**: iOS图标系统会自动添加圆角，请提供方形图标
3. **内容**: 图标应该简洁明了，能够清晰表达应用功能
4. **颜色**: 建议使用品牌色彩，确保在不同背景下都清晰可见

## 当前图标文件

请将对应尺寸的图标文件放置在此目录下，文件名格式如上所示。

## 生成工具推荐

- **在线工具**: App Icon Generator
- **设计软件**: Figma, Sketch, Adobe Illustrator
- **批量生成**: Icon Kitchen (Android), App Icon Generator (iOS)
