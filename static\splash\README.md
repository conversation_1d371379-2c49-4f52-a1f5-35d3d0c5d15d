# 启动页说明

## 启动页尺寸要求

### Android 启动页
- **hdpi**: 480x762px (splash-480x762.png)
- **xhdpi**: 720x1242px (splash-720x1242.png)
- **xxhdpi**: 1080x1882px (splash-1080x1882.png)

### iOS 启动页

#### iPhone
- **default**: 320x480px (splash-320x480.png) - iPhone 3GS
- **retina35**: 640x960px (splash-640x960.png) - iPhone 4/4S
- **retina40**: 640x1136px (splash-640x1136.png) - iPhone 5/5S/5C/SE
- **retina47**: 750x1334px (splash-750x1334.png) - iPhone 6/6S/7/8
- **retina55**: 1242x2208px (splash-1242x2208.png) - iPhone 6+/6S+/7+/8+
- **iphonex**: 1125x2436px (splash-1125x2436.png) - iPhone X/XS/11 Pro

#### iPad
- **portrait**: 1536x2048px (splash-1536x2048.png) - iPad 竖屏
- **landscape**: 2048x1536px (splash-2048x1536.png) - iPad 横屏

## 启动页设计要求

1. **格式**: PNG格式
2. **内容**: 
   - 应用Logo
   - 品牌标识
   - 简洁的背景设计
   - 避免文字内容（多语言适配）

3. **设计原则**:
   - 保持简洁
   - 突出品牌
   - 快速加载
   - 与应用主题一致

4. **注意事项**:
   - 启动页显示时间很短，不要放置过多信息
   - 考虑不同屏幕尺寸的适配
   - 避免使用需要网络加载的内容

## 当前启动页文件

请将对应尺寸的启动页文件放置在此目录下，文件名格式如上所示。

## 设计建议

- 使用应用的主色调作为背景
- 居中放置应用Logo
- 可以添加简单的品牌标语
- 保持与应用界面风格一致
