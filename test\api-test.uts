// API测试文件
import { get, post, login, checkLoginStatus, getCurrentEnv, getEnvConfig } from '../utils/request.uts'

// 测试环境配置
export function testEnvConfig(): void {
  console.log('=== 环境配置测试 ===')
  console.log('当前环境:', getCurrentEnv())
  console.log('环境配置:', getEnvConfig())
}

// 测试基础网络请求
export async function testBasicRequest(): Promise<void> {
  console.log('=== 基础请求测试 ===')
  
  try {
    // 测试GET请求
    const getData = await get('/test/get', { test: 'data' })
    console.log('GET请求结果:', getData)
    
    // 测试POST请求
    const postData = await post('/test/post', { name: '测试', value: 123 })
    console.log('POST请求结果:', postData)
    
  } catch (error) {
    console.error('基础请求测试失败:', error)
  }
}

// 测试登录功能
export async function testLogin(): Promise<void> {
  console.log('=== 登录功能测试 ===')
  
  try {
    // 检查登录状态
    const isLoggedIn = await checkLoginStatus()
    console.log('当前登录状态:', isLoggedIn)
    
    if (!isLoggedIn) {
      // 尝试登录
      const loginResult = await login({
        username: 'test_user',
        password: 'test123456'
      })
      console.log('登录结果:', loginResult)
    }
    
  } catch (error) {
    console.error('登录测试失败:', error)
  }
}

// 运行所有测试
export async function runAllTests(): Promise<void> {
  console.log('开始运行API测试...')
  
  // 测试环境配置
  testEnvConfig()
  
  // 测试基础请求
  await testBasicRequest()
  
  // 测试登录功能
  await testLogin()
  
  console.log('API测试完成')
}
