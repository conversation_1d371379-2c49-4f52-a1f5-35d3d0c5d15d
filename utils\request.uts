// 统一导出网络请求相关功能
export { get, post, put, del, request, requestInterceptor } from '../api/index.uts'

// 导出登录相关API
export {
  login,
  getCaptcha,
  logout,
  refreshToken,
  getUserInfo,
  checkLoginStatus,
  saveLoginInfo,
  clearLoginInfo,
  getLocalUserInfo
} from '../api/login.uts'

// 导出类型定义
export type {
  LoginParams,
  LoginResponse,
  UserInfo,
  CaptchaResponse,
  RefreshTokenResponse
} from '../api/login.uts'

// 导出环境配置相关
export { getEnvConfig, getCurrentEnv, EnvType } from '../config/env.uts'
export type { EnvConfig } from '../config/env.uts'
