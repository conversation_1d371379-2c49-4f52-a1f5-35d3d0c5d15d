import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  
  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    
    // 代理配置 - 解决跨域问题
    proxy: {
      // 代理所有 /api 请求
      '/api': {
        target: 'http://localhost:8080', // 后端服务地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/api'),
        // 配置代理日志
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
          })
        }
      },
      
      // 代理WebSocket连接
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
        changeOrigin: true
      },
      
      // 代理静态资源
      '/static': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/static/, '/static')
      },
      
      // 代理上传接口
      '/upload': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/upload/, '/upload')
      }
    }
  },
  
  // 构建配置
  build: {
    // 输出目录
    outDir: 'dist',
    
    // 生成源码映射
    sourcemap: process.env.NODE_ENV === 'development',
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 生产环境移除console
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true
      }
    },
    
    // 分包配置
    rollupOptions: {
      output: {
        // 分包策略
        manualChunks: {
          'vendor': ['vue', '@dcloudio/uni-app'],
          'utils': ['./utils/index.ts']
        }
      }
    }
  },
  
  // 环境变量配置
  define: {
    // 注入环境变量
    __APP_ENV__: JSON.stringify(process.env.NODE_ENV || 'development'),
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  },
  
  // CSS配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/uni.scss";`
      }
    }
  },
  
  // 路径别名
  resolve: {
    alias: {
      '@': '/src',
      '@/api': '/api',
      '@/config': '/config',
      '@/utils': '/utils',
      '@/components': '/components',
      '@/pages': '/pages',
      '@/static': '/static'
    }
  }
})
